
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Modern Combined Tools</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <style>
        /* Base styles */
        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
            background-color: #f3f4f6; /* Tailwind gray-100 */
            color: #1f2937; /* Tailwind gray-800 */
        }

        /* Consistent styling for sections */
        .tool-section {
            background-color: white;
            padding: 1.5rem; /* p-6 */
            border-radius: 0.5rem; /* rounded-lg */
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); /* shadow */
            margin-bottom: 1.5rem; /* space-y-6 equivalent */
        }

        /* Input field focus */
        input:focus, select:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
            --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
            --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
            box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
            border-color: transparent !important; /* Ensure focus ring is primary */
        }
        input[type="text"]:focus, input[type="number"]:focus {
             --tw-ring-color: #a5b4fc; /* Indigo-300 */
        }
        #oldNumberInput:focus, #numberInput:focus {
            --tw-ring-color: #86efac; /* Green-300 */
        }
         #timeDataSearchInput:focus {
            --tw-ring-color: #93c5fd; /* Blue-300 */
        }
        .n-input:focus { --tw-ring-color: #93c5fd; /* Blue-300 */ }
        .s-input:focus { --tw-ring-color: #fca5a5; /* Red-300 */ }

        /* Result display */
        .result-display {
            margin-top: 0.75rem; /* mt-3 */
            padding: 0.75rem; /* p-3 */
            border-radius: 0.375rem; /* rounded-md */
            background-color: #f9fafb; /* gray-50 */
            border: 1px solid #e5e7eb; /* border-gray-200 */
            min-height: 4rem;
            font-size: 0.875rem; /* text-sm */
            word-wrap: break-word;
        }
        .result-success { color: #059669; /* green-600 */ font-weight: 500; }
        .result-error { color: #dc2626; /* red-600 */ font-weight: 500; }
        .result-placeholder { color: #6b7280; /* gray-500 */ font-style: italic; }

        /* Candle search columns */
        .result-columns {
            display: flex;
            flex-wrap: wrap;
            gap: 1.25rem; /* gap-5 */
            margin-top: 0.5rem; /* mt-2 */
        }
        .result-column {
            flex: 1;
            min-width: 250px; /* Adjusted min-width */
            background-color: #f9fafb; /* gray-50 */
            padding: 1rem; /* p-4 */
            border-radius: 0.5rem; /* rounded-lg */
            border: 1px solid #e5e7eb; /* border-gray-200 */
        }
        .candle-times {
            margin-top: 0.5rem; /* mt-2 */
            font-size: 0.875rem; /* text-sm */
            word-wrap: break-word;
            max-height: 150px; /* Limit height */
            overflow-y: auto; /* Add scroll if needed */
        }

        /* Custom scrollbar for results */
        .results-scrollbar::-webkit-scrollbar { width: 6px; }
        .results-scrollbar::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 10px; }
        .results-scrollbar::-webkit-scrollbar-thumb { background: #a0aec0; border-radius: 10px; }
        .results-scrollbar::-webkit-scrollbar-thumb:hover { background: #718096; }

        /* Animations */
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes slideInUp { from { transform: translateY(20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.6; } }
        @keyframes flash { 0%, 100% { background-color: inherit; } 50% { background-color: #eef2ff; } } /* Subtle flash */

        .animate-fadeIn { animation: fadeIn 0.4s ease-out forwards; }
        .animate-fadeInUp { animation: fadeInUp 0.5s ease-out forwards; opacity: 0; }
        .animate-slideInUp { animation: slideInUp 0.5s ease-out forwards; opacity: 0; }
        .animate-pulse-custom { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        .animate-flash { animation: flash 0.8s ease-out; }

        /* Stagger animation delay */
        .staggered-list li { animation-delay: var(--stagger-delay, 0s); }

        /* Mode switching */
        .mode-section { transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out, visibility 0.3s; }
        .mode-section.hidden { opacity: 0; transform: translateY(10px); pointer-events: none; visibility: hidden; position: absolute; width: 100%; } /* Prevent layout shifts */
        .mode-button-active { background-color: #4f46e5 !important; /* indigo-600 */ color: white !important; }
        .mode-button-inactive { background-color: #6b7280; /* gray-500 */ }

        /* NIFTY/SENSEX inputs */
        .ns-input { width: 6rem; /* w-24 */ padding: 0.25rem; /* p-1 */ border-radius: 0.25rem; border: 1px solid #d1d5db; /* border-gray-300 */ }
        .ns-result { font-weight: 600; /* font-semibold */ width: 1rem; /* w-4 */ text-align: right; }

        /* Responsive adjustments */
        @media screen and (max-width: 640px) {
            .container { padding: 0.5rem; /* p-2 */ }
            .tool-section { padding: 1rem; /* p-4 */ }
            .result-column { min-width: 100%; }
            .candle-times { font-size: 0.8rem; padding: 0.4rem; }
            .mode-switch-buttons { flex-direction: column; gap: 0.5rem; /* gap-2 */ }
            .mode-switch-buttons button { width: 100%; }
            .ns-input { width: 5rem; }
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">
    <div class="container mx-auto p-4 max-w-4xl">

        <div class="flex flex-wrap justify-center gap-3 mb-6 mode-switch-buttons">
            <button data-mode="calculator" class="mode-button mode-button-inactive text-white p-2 rounded transition-colors duration-200 w-full md:w-auto">Calculator</button>
            <button data-mode="candle" class="mode-button mode-button-inactive text-white p-2 rounded transition-colors duration-200 w-full md:w-auto">Candle Search</button>
            <button data-mode="timeData" class="mode-button mode-button-inactive text-white p-2 rounded transition-colors duration-200 w-full md:w-auto">3min/24H Search</button>
        </div>

        <div class="relative">

            <div id="calculatorMode" class="mode-section space-y-6">
                <section class="tool-section animate-fadeInUp">
                    <h2 class="text-xl font-semibold mb-4 text-gray-700">Digital Root Calculator</h2>
                    <div class="space-y-3">
                        <input type="text" id="digitalRootInput" class="w-full p-2 border border-gray-300 rounded" placeholder="Enter number">
                        <div class="flex space-x-2">
                            <button id="calculateDigitalRootBtn" class="flex-1 bg-indigo-500 hover:bg-indigo-600 text-white p-2 rounded transition-colors">Calculate</button>
                            <button id="clearDigitalRootBtn" class="flex-1 bg-red-500 hover:bg-red-600 text-white p-2 rounded transition-colors">Clear</button>
                        </div>
                        <input type="text" id="digitalRootResult" class="w-full p-2 border border-gray-300 rounded bg-gray-100 font-medium" placeholder="Result" readonly>
                        <div>
                            <h3 class="font-medium text-gray-600">Last Calculation:</h3>
                            <div id="digitalRootHistory" class="mt-1 p-2 rounded bg-gray-50 text-sm min-h-[3rem] border border-gray-200">
                                <p class="result-placeholder animate-fadeIn">No calculation yet.</p>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="tool-section animate-fadeInUp" style="animation-delay: 0.1s;">
                    <h2 class="text-xl font-semibold mb-4 text-center text-gray-700">Number Search (Old Data)</h2>
                    <div class="flex flex-col items-center space-y-3">
                        <input type="number" id="oldNumberInput" class="p-2 border border-gray-300 rounded w-full max-w-xs text-center text-lg" placeholder="1-9" min="1" max="9">
                        <button id="findOldNumberBtn" class="bg-green-500 hover:bg-green-600 text-white p-2 rounded w-full max-w-xs transition-colors">
                            Search (Old)
                        </button>
                        <div id="oldSearchResult" class="result-display w-full max-w-2xl text-center">
                             <p class="result-placeholder">Enter a number (1-9) and click Search.</p>
                        </div>
                    </div>
                </section>

                <section class="tool-section animate-fadeInUp" style="animation-delay: 0.2s;">
                    <h2 class="text-xl font-semibold mb-4 text-gray-700">NIFTY & SENSEX Calculator</h2>
                    <div id="calculatorSections" class="space-y-4">
                        </div>
                    <button id="addNSSectionBtn" class="w-full bg-purple-500 hover:bg-purple-600 text-white p-2 rounded mt-4 transition-colors">Add NIFTY/SENSEX Section</button>
                </section>
            </div>

            <div id="candleMode" class="mode-section hidden">
                <section class="tool-section animate-fadeInUp">
                    <h2 class="text-xl font-semibold mb-4 text-center text-gray-700">5 & 15 Minute Candle Search</h2>
                    <div class="flex flex-col items-center space-y-3">
                        <input type="number" id="numberInput" class="p-2 border border-gray-300 rounded w-full max-w-xs text-center text-lg" placeholder="1-9" min="1" max="9">
                        <button id="findCandleNumberBtn" class="bg-green-500 hover:bg-green-600 text-white p-2 rounded w-full max-w-xs transition-colors">
                            Search Candles (5/15 Min)
                        </button>
                        <div id="candleResult" class="result-display w-full max-w-3xl">
                            <p class="result-placeholder">Enter a number (1-9) and click Search.</p>
                        </div>
                    </div>
                </section>
            </div>

            <div id="timeDataMode" class="mode-section hidden">
                <section class="tool-section animate-fadeInUp max-w-md mx-auto">
                    <h1 class="text-xl font-semibold text-center text-gray-700 mb-5">Time Data Search (3min/24H)</h1>
                    <div class="flex space-x-2 mb-4">
                        <input type="number" id="timeDataSearchInput" placeholder="Enter number (1-9)" min="1" max="9" class="flex-grow px-3 py-2 border border-gray-300 rounded-md transition duration-200">
                        <button id="timeDataSearchButton" class="px-4 py-2 bg-blue-500 text-white font-medium rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-offset-1 transition duration-200 shadow active:scale-95">Search</button>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-md border border-gray-200 h-64 overflow-y-auto results-scrollbar" id="timeDataResultsContainer">
                        <p class="result-placeholder text-center" id="timeDataPlaceholderText">Enter a number and click Search.</p>
                        <ul id="timeDataResultsList" class="space-y-1 text-gray-700 staggered-list"></ul>
                    </div>
                </section>
            </div>

        </div> </div> <script>
        // --- DATA (Keep as is for now, consider loading externally or processing if very large) ---
        const oldSearchData = { 9.15: 6, 9.17: 8, 9.19: 1, 9.21: 3, 9.23: 5, 9.25: 7, 9.27: 9, 9.29: 2, 9.31: 4, 9.33: 6, 9.35: 8, 9.37: 1, 9.39: 3, 9.41: 5, 9.43: 7, 9.45: 9, 9.47: 2, 9.49: 4, 9.51: 6, 9.53: 8, 9.55: 1, 9.57: 3, 9.59: 5, 10.01: 2, 10.03: 4, 10.05: 6, 10.07: 8, 10.09: 1, 10.11: 3, 10.13: 5, 10.15: 7, 10.17: 9, 10.19: 2, 10.21: 4, 10.23: 6, 10.25: 8, 10.27: 1, 10.29: 3, 10.31: 5, 10.33: 7, 10.35: 9, 10.37: 2, 10.39: 4, 10.41: 6, 10.43: 8, 10.45: 1, 10.47: 3, 10.49: 5, 10.51: 7, 10.53: 9, 10.55: 2, 10.57: 4, 10.59: 6, 11.01: 3, 11.03: 5, 11.05: 7, 11.07: 9, 11.09: 2, 11.11: 4, 11.13: 6, 11.15: 8, 11.17: 1, 11.19: 3, 11.21: 5, 11.23: 7, 11.25: 9, 11.27: 2, 11.29: 4, 11.31: 6, 11.33: 8, 11.35: 1, 11.37: 3, 11.39: 5, 11.41: 7, 11.43: 9, 11.45: 2, 11.47: 4, 11.49: 6, 11.51: 8, 11.53: 1, 11.55: 3, 11.57: 5, 11.59: 7, 12.01: 4, 12.03: 6, 12.05: 8, 12.07: 1, 12.09: 3, 12.11: 5, 12.13: 7, 12.15: 9, 12.17: 2, 12.19: 4, 12.21: 6, 12.23: 8, 12.25: 1, 12.27: 3, 12.29: 5, 12.31: 7, 12.33: 9, 12.35: 2, 12.37: 4, 12.39: 6, 12.41: 8, 12.43: 1, 12.45: 3, 12.47: 5, 12.49: 7, 12.51: 9, 12.53: 2, 12.55: 4, 12.57: 6, 12.59: 8, 13.01: 5, 13.03: 7, 13.05: 9, 13.07: 2, 13.09: 4, 13.11: 6, 13.13: 8, 13.15: 1, 13.17: 3, 13.19: 5, 13.21: 7, 13.23: 9, 13.25: 2, 13.27: 4, 13.29: 6, 13.31: 8, 13.33: 1, 13.35: 3, 13.37: 5, 13.39: 7, 13.41: 9, 13.43: 2, 13.45: 4, 13.47: 6, 13.49: 8, 13.51: 1, 13.53: 3, 13.55: 5, 13.57: 7, 13.59: 9, 14.01: 6, 14.03: 8, 14.05: 1, 14.07: 3, 14.09: 5, 14.11: 7, 14.13: 9, 14.15: 2, 14.17: 4, 14.19: 6, 14.21: 8, 14.23: 1, 14.25: 3, 14.27: 5, 14.29: 7, 14.31: 9, 14.33: 2, 14.35: 4, 14.37: 6, 14.39: 8, 14.41: 1, 14.43: 3, 14.45: 5, 14.47: 7, 14.49: 9, 14.51: 2, 14.53: 4, 14.55: 6, 14.57: 8, 14.59: 1, 15.01: 7, 15.03: 9, 15.05: 2, 15.07: 4, 15.09: 6, 15.11: 8, 15.13: 1, 15.15: 3, 15.17: 5, 15.19: 7, 15.21: 9, 15.23: 2, 15.25: 4, 15.27: 6, 15.29: 8, 15.31: 1 };
        const data5min = { '00:00': 9, '00:05': 5, '00:10': 1, '00:15': 6, '00:20': 2, '00:25': 7, '00:30': 3, '00:35': 8, '00:40': 4, '00:45': 9, '00:50': 5, '00:55': 1, '01:00': 1, '01:05': 6, '01:10': 2, '01:15': 7, '01:20': 3, '01:25': 8, '01:30': 4, '01:35': 9, '01:40': 5, '01:45': 1, '01:50': 6, '01:55': 2, '02:00': 2, '02:05': 7, '02:10': 3, '02:15': 8, '02:20': 4, '02:25': 9, '02:30': 5, '02:35': 1, '02:40': 6, '02:45': 2, '02:50': 7, '02:55': 3, '03:00': 3, '03:05': 8, '03:10': 4, '03:15': 9, '03:20': 5, '03:25': 1, '03:30': 6, '03:35': 2, '03:40': 7, '03:45': 3, '03:50': 8, '03:55': 4, '04:00': 4, '04:05': 9, '04:10': 5, '04:15': 1, '04:20': 6, '04:25': 2, '04:30': 7, '04:35': 3, '04:40': 8, '04:45': 4, '04:50': 9, '04:55': 5, '05:00': 5, '05:05': 1, '05:10': 6, '05:15': 2, '05:20': 7, '05:25': 3, '05:30': 8, '05:35': 4, '05:40': 9, '05:45': 5, '05:50': 1, '05:55': 6, '06:00': 6, '06:05': 2, '06:10': 7, '06:15': 3, '06:20': 8, '06:25': 4, '06:30': 9, '06:35': 5, '06:40': 1, '06:45': 6, '06:50': 2, '06:55': 7, '07:00': 7, '07:05': 3, '07:10': 8, '07:15': 4, '07:20': 9, '07:25': 5, '07:30': 1, '07:35': 6, '07:40': 2, '07:45': 7, '07:50': 3, '07:55': 8, '08:00': 8, '08:05': 4, '08:10': 9, '08:15': 5, '08:20': 1, '08:25': 6, '08:30': 2, '08:35': 7, '08:40': 3, '08:45': 8, '08:50': 4, '08:55': 9, '09:00': 9, '09:05': 5, '09:10': 1, '09:15': 6, '09:20': 2, '09:25': 7, '09:30': 3, '09:35': 8, '09:40': 4, '09:45': 9, '09:50': 5, '09:55': 1, '10:00': 1, '10:05': 6, '10:10': 2, '10:15': 7, '10:20': 3, '10:25': 8, '10:30': 4, '10:35': 9, '10:40': 5, '10:45': 1, '10:50': 6, '10:55': 2, '11:00': 2, '11:05': 7, '11:10': 3, '11:15': 8, '11:20': 4, '11:25': 9, '11:30': 5, '11:35': 1, '11:40': 6, '11:45': 2, '11:50': 7, '11:55': 3, '12:00': 3, '12:05': 8, '12:10': 4, '12:15': 9, '12:20': 5, '12:25': 1, '12:30': 6, '12:35': 2, '12:40': 7, '12:45': 3, '12:50': 8, '12:55': 4, '13:00': 4, '13:05': 9, '13:10': 5, '13:15': 1, '13:20': 6, '13:25': 2, '13:30': 7, '13:35': 3, '13:40': 8, '13:45': 4, '13:50': 9, '13:55': 5, '14:00': 5, '14:05': 1, '14:10': 6, '14:15': 2, '14:20': 7, '14:25': 3, '14:30': 8, '14:35': 4, '14:40': 9, '14:45': 5, '14:50': 1, '14:55': 6, '15:00': 6, '15:05': 2, '15:10': 7, '15:15': 3, '15:20': 8, '15:25': 4, '15:30': 9, '15:35': 5, '15:40': 1, '15:45': 6, '15:50': 2, '15:55': 7, '16:00': 7, '16:05': 3, '16:10': 8, '16:15': 4, '16:20': 9, '16:25': 5, '16:30': 1, '16:35': 6, '16:40': 2, '16:45': 7, '16:50': 3, '16:55': 8, '17:00': 8, '17:05': 4, '17:10': 9, '17:15': 5, '17:20': 1, '17:25': 6, '17:30': 2, '17:35': 7, '17:40': 3, '17:45': 8, '17:50': 4, '17:55': 9, '18:00': 9, '18:05': 5, '18:10': 1, '18:15': 6, '18:20': 2, '18:25': 7, '18:30': 3, '18:35': 8, '18:40': 4, '18:45': 9, '18:50': 5, '18:55': 1, '19:00': 1, '19:05': 6, '19:10': 2, '19:15': 7, '19:20': 3, '19:25': 8, '19:30': 4, '19:35': 9, '19:40': 5, '19:45': 1, '19:50': 6, '19:55': 2, '20:00': 2, '20:05': 7, '20:10': 3, '20:15': 8, '20:20': 4, '20:25': 9, '20:30': 5, '20:35': 1, '20:40': 6, '20:45': 2, '20:50': 7, '20:55': 3, '21:00': 3, '21:05': 8, '21:10': 4, '21:15': 9, '21:20': 5, '21:25': 1, '21:30': 6, '21:35': 2, '21:40': 7, '21:45': 3, '21:50': 8, '21:55': 4, '22:00': 4, '22:05': 9, '22:10': 5, '22:15': 1, '22:20': 6, '22:25': 2, '22:30': 7, '22:35': 3, '22:40': 8, '22:45': 4, '22:50': 9, '22:55': 5, '23:00': 5, '23:05': 1, '23:10': 6, '23:15': 2, '23:20': 7, '23:25': 3, '23:30': 8, '23:35': 4, '23:40': 9, '23:45': 5, '23:50': 1, '23:55': 6 };
        const data15min = { '00:00': 9, '00:15': 6, '00:30': 3, '00:45': 9, '01:00': 1, '01:15': 7, '01:30': 4, '01:45': 1, '02:00': 2, '02:15': 8, '02:30': 5, '02:45': 2, '03:00': 3, '03:15': 9, '03:30': 6, '03:45': 3, '04:00': 4, '04:15': 1, '04:30': 7, '04:45': 4, '05:00': 5, '05:15': 2, '05:30': 8, '05:45': 5, '06:00': 6, '06:15': 3, '06:30': 9, '06:45': 6, '07:00': 7, '07:15': 4, '07:30': 1, '07:45': 7, '08:00': 8, '08:15': 5, '08:30': 2, '08:45': 8, '09:00': 9, '09:15': 6, '09:30': 3, '09:45': 9, '10:00': 1, '10:15': 7, '10:30': 4, '10:45': 1, '11:00': 2, '11:15': 8, '11:30': 5, '11:45': 2, '12:00': 3, '12:15': 9, '12:30': 6, '12:45': 3, '13:00': 4, '13:15': 1, '13:30': 7, '13:45': 4, '14:00': 5, '14:15': 2, '14:30': 8, '14:45': 5, '15:00': 6, '15:15': 3, '15:30': 9, '15:45': 6, '16:00': 7, '16:15': 4, '16:30': 1, '16:45': 7, '17:00': 8, '17:15': 5, '17:30': 2, '17:45': 8, '18:00': 9, '18:15': 6, '18:30': 3, '18:45': 9, '19:00': 1, '19:15': 7, '19:30': 4, '19:45': 1, '20:00': 2, '20:15': 8, '20:30': 5, '20:45': 2, '21:00': 3, '21:15': 9, '21:30': 6, '21:45': 3, '22:00': 4, '22:15': 1, '22:30': 7, '22:45': 4, '23:00': 5, '23:15': 2, '23:30': 8, '23:45': 5 };
        const rawTimeData = `"05:30 ","8 "\n"05:33 ","2 "\n"05:36 ","5 "\n"05:39 ","8 "\n"05:42 ","2 "\n"05:45 ","5 "\n"05:48 ","8 "\n"05:51 ","2 "\n"05:54 ","5 "\n"05:57 ","8 "\n"06:00 ","6 "\n"06:03 ","9 "\n"06:06 ","3 "\n"06:09 ","6 "\n"06:12 ","9 "\n"06:15 ","3 "\n"06:18 ","6 "\n"06:21 ","9 "\n"06:24 ","3 "\n"06:27 ","6 "\n"06:30 ","9 "\n"06:33 ","3 "\n"06:36 ","6 "\n"06:39 ","9 "\n"06:42 ","3 "\n"06:45 ","6 "\n"06:48 ","9 "\n"06:51 ","3 "\n"06:54 ","6 "\n"06:57 ","9 "\n"07:00 ","7 "\n"07:03 ","1 "\n"07:06 ","4 "\n"07:09 ","7 "\n"07:12 ","1 "\n"07:15 ","4 "\n"07:18 ","7 "\n"07:21 ","1 "\n"07:24 ","4 "\n"07:27 ","7 "\n"07:30 ","1 "\n"07:33 ","4 "\n"07:36 ","7 "\n"07:39 ","1 "\n"07:42 ","4 "\n"07:45 ","7 "\n"07:48 ","1 "\n"07:51 ","4 "\n"07:54 ","7 "\n"07:57 ","1 "\n"08:00 ","8 "\n"08:03 ","2 "\n"08:06 ","5 "\n"08:09 ","8 "\n"08:12 ","2 "\n"08:15 ","5 "\n"08:18 ","8 "\n"08:21 ","2 "\n"08:24 ","5 "\n"08:27 ","8 "\n"08:30 ","2 "\n"08:33 ","5 "\n"08:36 ","8 "\n"08:39 ","2 "\n"08:42 ","5 "\n"08:45 ","8 "\n"08:48 ","2 "\n"08:51 ","5 "\n"08:54 ","8 "\n"08:57 ","2 "\n"09:00 ","9 "\n"09:03 ","3 "\n"09:06 ","6 "\n"09:09 ","9 "\n"09:12 ","3 "\n"09:15 ","6 "\n"09:18 ","9 "\n"09:21 ","3 "\n"09:24 ","6 "\n"09:27 ","9 "\n"09:30 ","3 "\n"09:33 ","6 "\n"09:36 ","9 "\n"09:39 ","3 "\n"09:42 ","6 "\n"09:45 ","9 "\n"09:48 ","3 "\n"09:51 ","6 "\n"09:54 ","9 "\n"09:57 ","3 "\n"10:00 ","1 "\n"10:03 ","4 "\n"10:06 ","7 "\n"10:09 ","1 "\n"10:12 ","4 "\n"10:15 ","7 "\n"10:18 ","1 "\n"10:21 ","4 "\n"10:24 ","7 "\n"10:27 ","1 "\n"10:30 ","4 "\n"10:33 ","7 "\n"10:36 ","1 "\n"10:39 ","4 "\n"10:42 ","7 "\n"10:45 ","1 "\n"10:48 ","4 "\n"10:51 ","7 "\n"10:54 ","1 "\n"10:57 ","4 "\n"11:00 ","2 "\n"11:03 ","5 "\n"11:06 ","8 "\n"11:09 ","2 "\n"11:12 ","5 "\n"11:15 ","8 "\n"11:18 ","2 "\n"11:21 ","5 "\n"11:24 ","8 "\n"11:27 ","2 "\n"11:30 ","5 "\n"11:33 ","8 "\n"11:36 ","2 "\n"11:39 ","5 "\n"11:42 ","8 "\n"11:45 ","2 "\n"11:48 ","5 "\n"11:51 ","8 "\n"11:54 ","2 "\n"11:57 ","5 "\n"12:00 ","3 "\n"12:03 ","6 "\n"12:06 ","9 "\n"12:09 ","3 "\n"12:12 ","6 "\n"12:15 ","9 "\n"12:18 ","3 "\n"12:21 ","6 "\n"12:24 ","9 "\n"12:27 ","3 "\n"12:30 ","6 "\n"12:33 ","9 "\n"12:36 ","3 "\n"12:39 ","6 "\n"12:42 ","9 "\n"12:45 ","3 "\n"12:48 ","6 "\n"12:51 ","9 "\n"12:54 ","3 "\n"12:57 ","6 "\n"13:00 ","4 "\n"13:03 ","7 "\n"13:06 ","1 "\n"13:09 ","4 "\n"13:12 ","7 "\n"13:15 ","1 "\n"13:18 ","4 "\n"13:21 ","7 "\n"13:24 ","1 "\n"13:27 ","4 "\n"13:30 ","7 "\n"13:33 ","1 "\n"13:36 ","4 "\n"13:39 ","7 "\n"13:42 ","1 "\n"13:45 ","4 "\n"13:48 ","7 "\n"13:51 ","1 "\n"13:54 ","4 "\n"13:57 ","7 "\n"14:00 ","5 "\n"14:03 ","8 "\n"14:06 ","2 "\n"14:09 ","5 "\n"14:12 ","8 "\n"14:15 ","2 "\n"14:18 ","5 "\n"14:21 ","8 "\n"14:24 ","2 "\n"14:27 ","5 "\n"14:30 ","8 "\n"14:33 ","2 "\n"14:36 ","5 "\n"14:39 ","8 "\n"14:42 ","2 "\n"14:45 ","5 "\n"14:48 ","8 "\n"14:51 ","2 "\n"14:54 ","5 "\n"14:57 ","8 "\n"15:00 ","6 "\n"15:03 ","9 "\n"15:06 ","3 "\n"15:09 ","6 "\n"15:12 ","9 "\n"15:15 ","3 "\n"15:18 ","6 "\n"15:21 ","9 "\n"15:24 ","3 "\n"15:27 ","6 "\n"15:30 ","9 "\n"15:33 ","3 "\n"15:36 ","6 "\n"15:39 ","9 "\n"15:42 ","3 "\n"15:45 ","6 "\n"15:48 ","9 "\n"15:51 ","3 "\n"15:54 ","6 "\n"15:57 ","9 "\n"16:00 ","7 "\n"16:03 ","1 "\n"16:06 ","4 "\n"16:09 ","7 "\n"16:12 ","1 "\n"16:15 ","4 "\n"16:18 ","7 "\n"16:21 ","1 "\n"16:24 ","4 "\n"16:27 ","7 "\n"16:30 ","1 "\n"16:33 ","4 "\n"16:36 ","7 "\n"16:39 ","1 "\n"16:42 ","4 "\n"16:45 ","7 "\n"16:48 ","1 "\n"16:51 ","4 "\n"16:54 ","7 "\n"16:57 ","1 "\n"17:00 ","8 "\n"17:03 ","2 "\n"17:06 ","5 "\n"17:09 ","8 "\n"17:12 ","2 "\n"17:15 ","5 "\n"17:18 ","8 "\n"17:21 ","2 "\n"17:24 ","5 "\n"17:27 ","8 "\n"17:30 ","2 "\n"17:33 ","5 "\n"17:36 ","8 "\n"17:39 ","2 "\n"17:42 ","5 "\n"17:45 ","8 "\n"17:48 ","2 "\n"17:51 ","5 "\n"17:54 ","8 "\n"17:57 ","2 "\n"18:00 ","9 "\n"18:03 ","3 "\n"18:06 ","6 "\n"18:09 ","9 "\n"18:12 ","3 "\n"18:15 ","6 "\n"18:18 ","9 "\n"18:21 ","3 "\n"18:24 ","6 "\n"18:27 ","9 "\n"18:30 ","3 "\n"18:33 ","6 "\n"18:36 ","9 "\n"18:39 ","3 "\n"18:42 ","6 "\n"18:45 ","9 "\n"18:48 ","3 "\n"18:51 ","6 "\n"18:54 ","9 "\n"18:57 ","3 "\n"19:00 ","1 "\n"19:03 ","4 "\n"19:06 ","7 "\n"19:09 ","1 "\n"19:12 ","4 "\n"19:15 ","7 "\n"19:18 ","1 "\n"19:21 ","4 "\n"19:24 ","7 "\n"19:27 ","1 "\n"19:30 ","4 "\n"19:33 ","7 "\n"19:36 ","1 "\n"19:39 ","4 "\n"19:42 ","7 "\n"19:45 ","1 "\n"19:48 ","4 "\n"19:51 ","7 "\n"19:54 ","1 "\n"19:57 ","4 "\n"20:00 ","2 "\n"20:03 ","5 "\n"20:06 ","8 "\n"20:09 ","2 "\n"20:12 ","5 "\n"20:15 ","8 "\n"20:18 ","2 "\n"20:21 ","5 "\n"20:24 ","8 "\n"20:27 ","2 "\n"20:30 ","5 "\n"20:33 ","8 "\n"20:36 ","2 "\n"20:39 ","5 "\n"20:42 ","8 "\n"20:45 ","2 "\n"20:48 ","5 "\n"20:51 ","8 "\n"20:54 ","2 "\n"20:57 ","5 "\n"21:00 ","3 "\n"21:03 ","6 "\n"21:06 ","9 "\n"21:09 ","3 "\n"21:12 ","6 "\n"21:15 ","9 "\n"21:18 ","3 "\n"21:21 ","6 "\n"21:24 ","9 "\n"21:27 ","3 "\n"21:30 ","6 "\n"21:33 ","9 "\n"21:36 ","3 "\n"21:39 ","6 "\n"21:42 ","9 "\n"21:45 ","3 "\n"21:48 ","6 "\n"21:51 ","9 "\n"21:54 ","3 "\n"21:57 ","6 "\n"22:00 ","4 "\n"22:03 ","7 "\n"22:06 ","1 "\n"22:09 ","4 "\n"22:12 ","7 "\n"22:15 ","1 "\n"22:18 ","4 "\n"22:21 ","7 "\n"22:24 ","1 "\n"22:27 ","4 "\n"22:30 ","7 "\n"22:33 ","1 "\n"22:36 ","4 "\n"22:39 ","7 "\n"22:42 ","1 "\n"22:45 ","4 "\n"22:48 ","7 "\n"22:51 ","1 "\n"22:54 ","4 "\n"22:57 ","7 "\n"23:00 ","5 "\n"23:03 ","8 "\n"23:06 ","2 "\n"23:09 ","5 "\n"23:12 ","8 "\n"23:15 ","2 "\n"23:18 ","5 "\n"23:21 ","8 "\n"23:24 ","2 "\n"23:27 ","5 "\n"23:30 ","8 "\n"23:33 ","2 "\n"23:36 ","5 "\n"23:39 ","8 "\n"23:42 ","2 "\n"23:45 ","5 "\n"23:48 ","8 "\n"23:51 ","2 "\n"23:54 ","5 "\n"23:57 ","8 "\n"00:00 ","9 "\n"00:03 ","3 "\n"00:06 ","6 "\n"00:09 ","9 "\n"00:12 ","3 "\n"00:15 ","6 "\n"00:18 ","9 "\n"00:21 ","3 "\n"00:24 ","6 "\n"00:27 ","9 "\n"00:30 ","3 "\n"00:33 ","6 "\n"00:36 ","9 "\n"00:39 ","3 "\n"00:42 ","6 "\n"00:45 ","9 "\n"00:48 ","3 "\n"00:51 ","6 "\n"00:54 ","9 "\n"00:57 ","3 "\n"01:00 ","1 "\n"01:03 ","4 "\n"01:06 ","7 "\n"01:09 ","1 "\n"01:12 ","4 "\n"01:15 ","7 "\n"01:18 ","1 "\n"01:21 ","4 "\n"01:24 ","7 "\n"01:27 ","1 "\n"01:30 ","4 "\n"01:33 ","7 "\n"01:36 ","1 "\n"01:39 ","4 "\n"01:42 ","7 "\n"01:45 ","1 "\n"01:48 ","4 "\n"01:51 ","7 "\n"01:54 ","1 "\n"01:57 ","4 "\n"02:00 ","2 "\n"02:03 ","5 "\n"02:06 ","8 "\n"02:09 ","2 "\n"02:12 ","5 "\n"02:15 ","8 "\n"02:18 ","2 "\n"02:21 ","5 "\n"02:24 ","8 "\n"02:27 ","2 "\n"02:30 ","5 "\n"02:33 ","8 "\n"02:36 ","2 "\n"02:39 ","5 "\n"02:42 ","8 "\n"02:45 ","2 "\n"02:48 ","5 "\n"02:51 ","8 "\n"02:54 ","2 "\n"02:57 ","5 "\n"03:00 ","3 "\n"03:03 ","6 "\n"03:06 ","9 "\n"03:09 ","3 "\n"03:12 ","6 "\n"03:15 ","9 "\n"03:18 ","3 "\n"03:21 ","6 "\n"03:24 ","9 "\n"03:27 ","3 "\n"03:30 ","6 "\n"03:33 ","9 "\n"03:36 ","3 "\n"03:39 ","6 "\n"03:42 ","9 "\n"03:45 ","3 "\n"03:48 ","6 "\n"03:51 ","9 "\n"03:54 ","3 "\n"03:57 ","6 "\n"04:00 ","4 "\n"04:03 ","7 "\n"04:06 ","1 "\n"04:09 ","4 "\n"04:12 ","7 "\n"04:15 ","1 "\n"04:18 ","4 "\n"04:21 ","7 "\n"04:24 ","1 "\n"04:27 ","4 "\n"04:30 ","7 "\n"04:33 ","1 "\n"04:36 ","4 "\n"04:39 ","7 "\n"04:42 ","1 "\n"04:45 ","4 "\n"04:48 ","7 "\n"04:51 ","1 "\n"04:54 ","4 "\n"04:57 ","7 "\n"05:00 ","5 "\n"05:03 ","8 "\n"05:06 ","2 "\n"05:09 ","5 "\n"05:12 ","8 "\n"05:15 ","2 "\n"05:18 ","5 "\n"05:21 ","8 "\n"05:24 ","2 "\n"05:27 ","5 "\n\nSources and related content\n3min24Ho\n\nPDF `;

        // --- State ---
        let lastDigitalRootCalculation = null;
        let timeDataParsed = []; // Will hold { time: 'HH:MM', number: 'N' }

        // --- DOM Element References ---
        const getEl = (id) => document.getElementById(id);
        const querySel = (selector) => document.querySelector(selector);
        const querySelAll = (selector) => document.querySelectorAll(selector);

        const elements = {
            modeSwitchButtons: querySelAll('.mode-switch-buttons button'),
            modeSections: {
                calculator: getEl('calculatorMode'),
                candle: getEl('candleMode'),
                timeData: getEl('timeDataMode')
            },
            digitalRoot: {
                input: getEl('digitalRootInput'),
                result: getEl('digitalRootResult'),
                history: getEl('digitalRootHistory'),
                calculateBtn: getEl('calculateDigitalRootBtn'),
                clearBtn: getEl('clearDigitalRootBtn')
            },
            oldSearch: {
                input: getEl('oldNumberInput'),
                resultDiv: getEl('oldSearchResult'),
                searchBtn: getEl('findOldNumberBtn')
            },
            nsCalculator: {
                sectionsDiv: getEl('calculatorSections'),
                addBtn: getEl('addNSSectionBtn')
            },
            candleSearch: {
                input: getEl('numberInput'),
                resultDiv: getEl('candleResult'),
                searchBtn: getEl('findCandleNumberBtn')
            },
            timeDataSearch: {
                input: getEl('timeDataSearchInput'),
                searchBtn: getEl('timeDataSearchButton'),
                resultsList: getEl('timeDataResultsList'),
                placeholder: getEl('timeDataPlaceholderText'),
                container: getEl('timeDataResultsContainer')
            }
        };

        // --- UTILITY FUNCTIONS ---

        /**
         * Calculates the digital root of a number string.
         * @param {string} numStr - The input number as a string.
         * @returns {number|null} The digital root, or null if input is invalid.
         */
        const getDigitalRoot = (numStr) => {
            const num = parseInt(numStr, 10);
            if (isNaN(num)) return null;
            if (num === 0) return 0;
            return (num - 1) % 9 + 1;
        };

        /**
         * Filters an object's entries by value.
         * @param {object} obj - The object to filter.
         * @param {*} value - The value to match.
         * @returns {string[]} An array of keys whose values match.
         */
        const filterObjectByValue = (obj, value) =>
            Object.entries(obj)
                  .filter(([, num]) => num === value)
                  .map(([key]) => key);

        /**
         * Displays a message (success, error, or placeholder) in a target element.
         * @param {HTMLElement} element - The target display element.
         * @param {string} message - The message content (HTML allowed).
         * @param {'success' | 'error' | 'placeholder' | 'info'} type - The type of message.
         */
        const displayMessage = (element, message, type = 'info') => {
            let cssClass = '';
            switch (type) {
                case 'success': cssClass = 'result-success'; break;
                case 'error': cssClass = 'result-error'; break;
                case 'placeholder': cssClass = 'result-placeholder'; break;
                default: cssClass = ''; // Default styling or none
            }
            // Apply animation and content
            element.innerHTML = `<div class="${cssClass} animate-fadeIn">${message}</div>`;
            // Flash effect
            element.classList.add('animate-flash');
            setTimeout(() => element.classList.remove('animate-flash'), 800);

             // Send results to Android interface if available
            if (window.Android) {
                // Send empty string for errors or placeholders indicating no results
                const sendEmpty = type === 'error' || (type === 'placeholder' && message.toLowerCase().includes('no match'));
                 window.Android.setSearchResults(sendEmpty ? '' : message.replace(/<[^>]*>/g, '')); // Strip HTML tags for Android
            }
        };

        /**
         * Parses the raw time data string into a structured array.
         */
        const parseTimeData = () => {
            timeDataParsed = rawTimeData
                .split('\n')
                .map(line => line.trim())
                .filter(line => line && !line.startsWith('--- PAGE')) // Filter empty lines and page breaks
                .map(line => {
                    const match = line.match(/"?(\d{2}:\d{2})\s*",?"\s*(\d)\s*"?/);
                    return match && match[1] && match[2] ? { time: match[1], number: match[2] } : null;
                })
                .filter(entry => entry !== null); // Filter out lines that didn't match
            // console.log("Parsed Time Data:", timeDataParsed); // For debugging
        };

        /**
         * Validates if a value is a number between 1 and 9.
         * @param {string|number} value - The value to validate.
         * @returns {number|null} The parsed number if valid, otherwise null.
         */
        const validateNumberInput = (value) => {
            const num = parseInt(value, 10);
            return !isNaN(num) && num >= 1 && num <= 9 ? num : null;
        };

        // --- CORE FUNCTIONS ---

        /* Digital Root Calculator */
        const handleCalculateDigitalRoot = () => {
            const inputVal = elements.digitalRoot.input.value;
            const result = getDigitalRoot(inputVal);

            if (result === null) {
                elements.digitalRoot.result.value = '';
                lastDigitalRootCalculation = null;
                displayMessage(elements.digitalRoot.history, 'Invalid input.', 'error');
                if (window.Android) window.Android.setSearchResults('');
                return;
            }

            elements.digitalRoot.result.value = result;
            lastDigitalRootCalculation = { input: inputVal, result };
            displayDigitalRootHistory();

            if (window.Android) {
                window.Android.setSearchResults(`Digital Root: Input ${inputVal} = ${result}`);
            }
        };

        const handleClearDigitalRoot = () => {
            elements.digitalRoot.input.value = '';
            elements.digitalRoot.result.value = '';
            lastDigitalRootCalculation = null;
            displayDigitalRootHistory();
            if (window.Android) window.Android.setSearchResults('');
        };

        const displayDigitalRootHistory = () => {
            elements.digitalRoot.history.innerHTML = ''; // Clear previous
            if (lastDigitalRootCalculation) {
                const div = document.createElement('div');
                div.className = 'p-1 border-l-2 border-indigo-200 pl-2 animate-fadeInUp';
                div.textContent = `Input: ${lastDigitalRootCalculation.input} → Result: ${lastDigitalRootCalculation.result}`;
                elements.digitalRoot.history.appendChild(div);
            } else {
                displayMessage(elements.digitalRoot.history, 'No calculation yet.', 'placeholder');
            }
        };

        /* Old Number Search */
        const handleFindOldNumber = () => {
            const value = validateNumberInput(elements.oldSearch.input.value);
            const resultDiv = elements.oldSearch.resultDiv;

            if (value === null) {
                displayMessage(resultDiv, 'Please enter a number between 1-9.', 'error');
                return;
            }

            const matchingTimes = filterObjectByValue(oldSearchData, value);
            let resultHTML = `<strong class="block mb-1 result-success">Results for ${value} (Old Data):</strong>`;
            let androidResultText = `Old Search ${value}: `;

            if (matchingTimes.length > 0) {
                resultHTML += `<div class="p-2 bg-gray-100 rounded mt-1 text-sm">${matchingTimes.join(', ')}</div>`;
                androidResultText += matchingTimes.join(', ');
                displayMessage(resultDiv, resultHTML, 'success');
                 if (window.Android) window.Android.setSearchResults(androidResultText);
            } else {
                resultHTML += '<div class="p-2 bg-gray-100 rounded mt-1 text-sm text-gray-500">No matches found.</div>';
                displayMessage(resultDiv, resultHTML, 'info'); // Use 'info' or 'placeholder'
                if (window.Android) window.Android.setSearchResults(''); // No matches
            }
        };

        /* NIFTY & SENSEX Calculator */
        const updateNSResults = (section) => {
            section.querySelectorAll('input[type="number"]').forEach(input => {
                const resultSpan = input.closest('.flex').querySelector('.ns-result');
                if (!resultSpan) return; // Safety check

                const val = Number(input.value);
                const root = isNaN(val) ? '' : getDigitalRoot(val);

                if (resultSpan.textContent !== String(root)) {
                    resultSpan.textContent = root;
                    resultSpan.classList.add('animate-fadeIn');
                    setTimeout(() => resultSpan.classList.remove('animate-fadeIn'), 400);
                }
            });
        };

        const createNSInputRow = (label, type, initialValue = 0) => `
            <div class="flex items-center justify-between gap-2">
                <span class="text-sm">${label}</span>
                <input type="number" class="ns-input ${type}-input" value="${initialValue}">
                <span class="ns-result"></span>
            </div>`;

        const addNSSection = () => {
            const newSection = document.createElement('div');
            newSection.className = 'grid grid-cols-1 md:grid-cols-2 gap-4 calculator-section animate-slideInUp';
            newSection.innerHTML = `
                <div class="bg-blue-50 p-3 rounded border border-blue-200 ns-sub-section">
                    <h3 class="text-blue-700 font-semibold mb-2">NIFTY</h3>
                    <div class="space-y-2">
                        ${createNSInputRow('OPEN', 'n', 23173)}
                        ${createNSInputRow('HIGH', 'n', 23183)}
                        ${createNSInputRow('LOW', 'n', 23168)}
                        ${createNSInputRow('CLOSE', 'n', 23175)}
                    </div>
                </div>
                <div class="bg-red-50 p-3 rounded border border-red-200 ns-sub-section">
                    <h3 class="text-red-700 font-semibold mb-2">SENSEX</h3>
                    <div class="space-y-2">
                        ${createNSInputRow('OPEN', 's', 76568)}
                        ${createNSInputRow('HIGH', 's', 76596)}
                        ${createNSInputRow('LOW', 's', 76533)}
                        ${createNSInputRow('CLOSE', 's', 76533)}
                    </div>
                </div>`;

            elements.nsCalculator.sectionsDiv.appendChild(newSection);
            updateNSResults(newSection); // Calculate initial roots for the new section
            newSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        };

        /* 5 & 15 Minute Candle Search */
        const handleFindCandleNumber = () => {
            const value = validateNumberInput(elements.candleSearch.input.value);
            const resultDiv = elements.candleSearch.resultDiv;

            if (value === null) {
                displayMessage(resultDiv, 'Please enter a number between 1-9.', 'error');
                return;
            }

            const matchingTimes5min = filterObjectByValue(data5min, value);
            const matchingTimes15min = filterObjectByValue(data15min, value);

            let resultHTML = `<div class="result-success animate-fadeIn">Results for Candle Value ${value}:</div>`;
            resultHTML += '<div class="result-columns animate-fadeInUp" style="animation-delay: 0.1s;">';
            resultHTML += `
                <div class="result-column">
                    <strong>5-Minute Intervals:</strong>
                    <div class="candle-times results-scrollbar">
                        ${matchingTimes5min.length > 0 ? matchingTimes5min.join(', ') : '<span class="text-gray-500">No matches</span>'}
                    </div>
                </div>`;
            resultHTML += `
                <div class="result-column">
                    <strong>15-Minute Intervals:</strong>
                    <div class="candle-times results-scrollbar">
                        ${matchingTimes15min.length > 0 ? matchingTimes15min.join(', ') : '<span class="text-gray-500">No matches</span>'}
                    </div>
                </div>`;
            resultHTML += '</div>';

            resultDiv.innerHTML = resultHTML; // Display the structured result

            // Prepare text for Android interface
            if (window.Android) {
                if (matchingTimes5min.length > 0 || matchingTimes15min.length > 0) {
                    let resultText = `Candle Search ${value}:\n`;
                    resultText += `5-Min: ${matchingTimes5min.length > 0 ? matchingTimes5min.join(', ') : 'None'}\n`;
                    resultText += `15-Min: ${matchingTimes15min.length > 0 ? matchingTimes15min.join(', ') : 'None'}`;
                    window.Android.setSearchResults(resultText);
                } else {
                    window.Android.setSearchResults(''); // No results
                }
            }
        };

        /* Time Data Search (3min/24H) */
        const handleTimeDataSearch = () => {
            const searchTerm = elements.timeDataSearch.input.value.trim();
            const listEl = elements.timeDataSearch.resultsList;
            const placeholderEl = elements.timeDataSearch.placeholder;
            const containerEl = elements.timeDataSearch.container;

            listEl.innerHTML = ''; // Clear previous results
            placeholderEl.style.display = 'none';
            placeholderEl.classList.remove('animate-pulse-custom');

            const searchNum = validateNumberInput(searchTerm);

            if (searchNum === null) {
                placeholderEl.textContent = 'Please enter a valid number (1-9).';
                placeholderEl.style.display = 'block';
                placeholderEl.classList.add('animate-pulse-custom');
                if (window.Android) window.Android.setSearchResults('');
                return;
            }

            const foundEntries = timeDataParsed.filter(entry => entry.number === String(searchNum)); // Compare as string

            if (foundEntries.length > 0) {
                const fragment = document.createDocumentFragment();
                foundEntries.forEach((entry, index) => {
                    const listItem = document.createElement('li');
                    listItem.textContent = entry.time;
                    listItem.className = 'px-2 py-1 rounded hover:bg-gray-100 animate-fadeInUp';
                    listItem.style.setProperty('--stagger-delay', `${index * 0.03}s`); // Stagger animation
                    fragment.appendChild(listItem);
                });
                listEl.appendChild(fragment); // Append all at once for performance

                if (window.Android) {
                    const resultText = `3min/24H Search ${searchNum}: ${foundEntries.map(e => e.time).join(', ')}`;
                    window.Android.setSearchResults(resultText);
                }
            } else {
                placeholderEl.textContent = `No time entries found for number '${searchNum}'.`;
                placeholderEl.style.display = 'block';
                placeholderEl.classList.add('animate-pulse-custom');
                 if (window.Android) window.Android.setSearchResults(`3min/24H Search ${searchNum}: No matches`);
            }

            // Flash the container on update
            containerEl.classList.add('animate-flash');
            setTimeout(() => containerEl.classList.remove('animate-flash'), 800);
        };

        /* Mode Switching */
        const switchMode = (selectedMode) => {
            Object.keys(elements.modeSections).forEach(modeKey => {
                const section = elements.modeSections[modeKey];
                const button = querySel(`.mode-switch-buttons button[data-mode="${modeKey}"]`);

                if (modeKey === selectedMode) {
                    section.classList.remove('hidden');
                    // Trigger reflow might not be needed, but can ensure animation restarts
                    // void section.offsetWidth;
                    section.classList.add('animate-fadeIn');
                    button?.classList.replace('mode-button-inactive', 'mode-button-active');
                } else {
                    section.classList.add('hidden');
                    section.classList.remove('animate-fadeIn');
                    button?.classList.replace('mode-button-active', 'mode-button-inactive');
                }
            });

            // Clear results from other modes for a cleaner switch (optional, but good UX)
            // displayMessage(elements.oldSearch.resultDiv, 'Enter a number (1-9) and click Search.', 'placeholder');
            // displayMessage(elements.candleSearch.resultDiv, 'Enter a number (1-9) and click Search.', 'placeholder');
            // elements.timeDataSearch.resultsList.innerHTML = '';
            // elements.timeDataSearch.placeholder.textContent = 'Enter a number and click Search.';
            // elements.timeDataSearch.placeholder.style.display = 'block';
            // elements.timeDataSearch.placeholder.classList.remove('animate-pulse-custom');

            // Clear Android results on mode switch
            if (window.Android) { window.Android.setSearchResults(''); }
        };

        // --- EVENT LISTENERS ---
        const addEnterKeyListener = (inputElement, buttonElement) => {
            inputElement.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    event.preventDefault(); // Prevent default form submission (if any)
                    buttonElement.click(); // Trigger the button's click handler
                }
            });
        };

        document.addEventListener('DOMContentLoaded', () => {
            // Parse data
            parseTimeData();

            // Mode Switching Buttons
            elements.modeSwitchButtons.forEach(button => {
                button.addEventListener('click', () => switchMode(button.dataset.mode));
            });

            // Digital Root
            elements.digitalRoot.calculateBtn.addEventListener('click', handleCalculateDigitalRoot);
            elements.digitalRoot.clearBtn.addEventListener('click', handleClearDigitalRoot);
            addEnterKeyListener(elements.digitalRoot.input, elements.digitalRoot.calculateBtn);

            // Old Search
            elements.oldSearch.searchBtn.addEventListener('click', handleFindOldNumber);
            addEnterKeyListener(elements.oldSearch.input, elements.oldSearch.searchBtn);

            // NIFTY/SENSEX
            elements.nsCalculator.addBtn.addEventListener('click', addNSSection);
            // Use event delegation for dynamically added inputs
            elements.nsCalculator.sectionsDiv.addEventListener('input', (event) => {
                if (event.target.matches('.n-input, .s-input')) {
                    // Find the parent calculator-section and update its results
                    const section = event.target.closest('.calculator-section');
                    if(section) updateNSResults(section);
                }
            });
            addNSSection(); // Add the first NIFTY/SENSEX section initially

            // Candle Search
            elements.candleSearch.searchBtn.addEventListener('click', handleFindCandleNumber);
            addEnterKeyListener(elements.candleSearch.input, elements.candleSearch.searchBtn);

            // Time Data Search
            elements.timeDataSearch.searchBtn.addEventListener('click', handleTimeDataSearch);
            addEnterKeyListener(elements.timeDataSearch.input, elements.timeDataSearch.searchBtn);
            // Optional: Clear pulse animation on input
            elements.timeDataSearch.input.addEventListener('input', () => {
                elements.timeDataSearch.placeholder.classList.remove('animate-pulse-custom');
            });

            // Set initial mode
            switchMode('calculator'); // Default to calculator mode
        });

    </script>
</body>
</html>
