
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Grocery Master</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.1/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .grocery-header {
            background: linear-gradient(90deg, #4285f4, #34a853);
            color: white;
        }
        
        .grocery-list-header {
            background-color: #11823b;
            color: white;
        }
        
        .calculator-container {
            background-color: #1a2332;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 0 20px #00c3ff;
        }
        
        .calculator-screen {
            background-color: #232b3c;
            color: #00c3ff;
            border-radius: 8px;
            padding: 15px;
            font-size: 2rem;
            text-align: right;
            margin-bottom: 15px;
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
        }
        
        .calculator-button {
            background-color: #3a4452;
            color: white;
            border-radius: 8px;
            font-size: 1.5rem;
            transition: all 0.2s;
        }
        
        .calculator-button:hover {
            background-color: #4a5568;
        }
        
        .calculator-button.operator {
            background-color: #00c3ff;
            color: #232b3c;
        }
        
        .calculator-button.special {
            background-color: #ff5757;
            color: white;
        }
        
        .calculator-button.equals {
            background-color: #00c3ff;
            color: #232b3c;
        }
        
        .history-item {
            border-left: 4px solid #4285f4;
            background-color: #f1f5f9;
        }
        
        .money-to-quantity {
            background-color: #e6f0ff;
            border-left: 4px solid #4285f4;
        }
        
        .quantity-to-cost {
            background-color: #e6f7ef;
            border-left: 4px solid #34a853;
        }
        
        .green-button {
            background-color: #34a853;
            color: white;
        }
        
        .blue-button {
            background-color: #4285f4;
            color: white;
        }
        
        .red-button {
            background-color: #ea4335;
            color: white;
        }
        
        .gray-button {
            background-color: #f1f3f4;
            color: #202124;
        }
        
        .calculator-power {
            color: #0f0;
            font-size: 1.2rem;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        
        .discount-badge {
            background-color: #ff9800;
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            margin-left: 0.5rem;
        }
        
        .discount-input {
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            padding: 0.5rem;
            width: 100%;
            margin-top: 0.5rem;
        }
        
        /* Hotel Bill Style */
        .hotel-bill {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            font-family: 'Courier New', monospace;
        }
        
        .hotel-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px dashed #000;
            padding-bottom: 10px;
        }
        
        .hotel-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .hotel-subtitle {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .hotel-date {
            font-size: 12px;
        }
        
        .hotel-items {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .hotel-items th {
            text-align: left;
            padding: 5px 0;
            border-bottom: 1px dashed #000;
        }
        
        .hotel-items td {
            padding: 5px 0;
            border-bottom: 1px dashed #ccc;
        }
        
        .hotel-items .item-name {
            width: 50%;
        }
        
        .hotel-items .item-qty {
            width: 15%;
            text-align: center;
        }
        
        .hotel-items .item-price {
            width: 15%;
            text-align: right;
        }
        
        .hotel-items .item-total {
            width: 20%;
            text-align: right;
        }
        
        .hotel-totals {
            width: 100%;
            margin-top: 20px;
            border-top: 2px dashed #000;
            padding-top: 10px;
        }
        
        .hotel-totals tr td {
            padding: 5px 0;
        }
        
        .hotel-totals .total-label {
            text-align: right;
            padding-right: 10px;
        }
        
        .hotel-totals .total-value {
            text-align: right;
            font-weight: bold;
        }
        
        .hotel-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            border-top: 2px dashed #000;
            padding-top: 10px;
        }
        
        .hotel-savings {
            color: #34a853;
            font-weight: bold;
        }

        /* Age Calculator Styles */
        .age-calculator-header {
            background: linear-gradient(90deg, #8e44ad, #3498db);
            color: white;
        }
        
        .date-input {
            letter-spacing: 1px;
            text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .date-input::placeholder {
            letter-spacing: normal;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        .month-display {
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            color: #888;
            font-size: 0.9rem;
            pointer-events: none;
        }
        
        .age-result {
            background: linear-gradient(135deg, #8e44ad, #3498db);
            color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .age-unit {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .age-value {
            font-size: 2.5rem;
            font-weight: bold;
            line-height: 1;
        }
        
        @media print {
            .no-print {
                display: none;
            }
            
            body {
                background-color: white;
            }
            
            .container {
                width: 100%;
                max-width: 100%;
            }
            
            .hotel-bill {
                box-shadow: none;
                padding: 0;
            }
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold mb-2">Ultimate Grocery Master</h1>
            <p class="text-gray-600">All-in-one grocery management solution</p>
        </div>
        
        <div class="flex flex-wrap mb-6 no-print">
            <button id="tab-grocery-calc" class="px-6 py-3 bg-blue-500 text-white rounded-t-lg mr-1 active">
                <i class="fas fa-calculator mr-2"></i>Grocery Calculator
            </button>
            <button id="tab-grocery-list" class="px-6 py-3 bg-green-600 text-white rounded-t-lg mr-1">
                <i class="fas fa-shopping-cart mr-2"></i>Grocery List
            </button>
            <button id="tab-neon-calc" class="px-6 py-3 bg-indigo-700 text-white rounded-t-lg mr-1">
                <i class="fas fa-calculator mr-2"></i>Neon Calculator
            </button>
            <button id="tab-age-calc" class="px-6 py-3 bg-purple-600 text-white rounded-t-lg">
                <i class="fas fa-birthday-cake mr-2"></i>Age Calculator
            </button>
        </div>
        
        <div id="grocery-calculator" class="tab-content active">
            <div class="grocery-header p-6 rounded-lg shadow-md mb-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-2xl font-bold">Grocery Calculator</h2>
                        <p class="text-white text-opacity-90">Smart calculations for your shopping</p>
                    </div>
                    <div>
                        <i class="fas fa-shopping-cart text-3xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white p-6 rounded-lg shadow-md money-to-quantity">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-coins text-blue-500"></i>
                        </div>
                        <h3 class="text-xl font-bold">Money to Quantity</h3>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Price per 1kg (₹)</label>
                        <div class="relative">
                            <input type="number" id="price-per-kg" class="w-full p-3 border rounded-lg pr-16" value="1000">
                            <span class="absolute right-3 top-3 text-gray-500">₹/kg</span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Your Money (₹)</label>
                        <div class="relative">
                            <input type="number" id="your-money" class="w-full p-3 border rounded-lg pr-8" value="720">
                            <span class="absolute right-3 top-3 text-gray-500">₹</span>
                        </div>
                    </div>
                    
                    <button id="calculate-quantity" class="w-full py-3 px-4 blue-button rounded-lg font-bold transition duration-200 flex items-center justify-center">
                        <i class="fas fa-balance-scale-right mr-2"></i> Calculate Quantity
                    </button>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md quantity-to-cost">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-weight text-green-500"></i>
                        </div>
                        <h3 class="text-xl font-bold">Quantity to Cost</h3>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Desired Quantity</label>
                        <div class="relative">
                            <input type="number" id="desired-quantity" class="w-full p-3 border rounded-lg pr-16" value="500">
                            <span class="absolute right-3 top-3 text-gray-500">grams</span>
                        </div>
                    </div>
                    
                    <div class="mb-4 opacity-0"> <label class="block text-gray-700 mb-2">Placeholder</label>
                        <div class="relative">
                            <input type="text" class="w-full p-3 border rounded-lg" disabled>
                        </div>
                    </div>
                    
                    <button id="calculate-cost" class="w-full py-3 px-4 green-button rounded-lg font-bold transition duration-200 flex items-center justify-center">
                        <i class="fas fa-calculator mr-2"></i> Calculate Cost
                    </button>
                </div>
            </div>
            
            <div class="flex space-x-4 mb-6">
                <button id="reset-calc" class="flex-1 py-3 px-4 gray-button rounded-lg font-bold transition duration-200">
                    <i class="fas fa-sync-alt mr-2"></i> Reset
                </button>
                <button id="clear-history" class="flex-1 py-3 px-4 gray-button rounded-lg font-bold transition duration-200">
                    <i class="fas fa-trash-alt mr-2"></i> Clear History
                </button>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">Recent Calculations</h3>
                    <span id="history-count" class="bg-gray-200 px-3 py-1 rounded-full text-sm">0 items</span>
                </div>
                
                <div id="calculation-history" class="space-y-3">
                    </div>
            </div>
        </div>
        
        <div id="grocery-list" class="tab-content">
            <div class="grocery-list-header p-6 rounded-lg shadow-md mb-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-2xl font-bold">Smart Grocery List</h2>
                        <p class="text-white text-opacity-90">Track your shopping efficiently</p>
                    </div>
                    <div>
                        <i class="fas fa-clipboard-list text-3xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-green-600 mb-4">Add New Item</h3>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Item Name</label>
                        <input type="text" id="item-name" class="w-full p-3 border rounded-lg">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-gray-700 mb-2">Price ($)</label>
                            <input type="number" id="item-price" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Quantity</label>
                            <input type="number" id="item-quantity" class="w-full p-3 border rounded-lg" value="1">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Category</label>
                        <select id="item-category" class="w-full p-3 border rounded-lg">
                            <option value="Produce">Produce</option>
                            <option value="Dairy">Dairy</option>
                            <option value="Meat">Meat</option>
                            <option value="Bakery">Bakery</option>
                            <option value="Pantry">Pantry</option>
                            <option value="Frozen">Frozen</option>
                            <option value="Beverages">Beverages</option>
                            <option value="Household">Household</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Discount (optional)</label>
                        <select id="discount-type" class="w-full p-3 border rounded-lg mb-2">
                            <option value="none">No discount</option>
                            <option value="percentage">Percentage discount</option>
                            <option value="fixed">Fixed amount discount</option>
                            <option value="bundle">Bundle discount (e.g. buy X get Y)</option>
                        </select>
                        
                        <div id="discount-details" class="hidden">
                            <div id="percentage-discount" class="discount-input hidden">
                                <label class="block text-gray-700 mb-1">Discount %</label>
                                <input type="number" id="discount-percentage" class="w-full p-2 border rounded" placeholder="e.g. 15">
                            </div>
                            
                            <div id="fixed-discount" class="discount-input hidden">
                                <label class="block text-gray-700 mb-1">Discount Amount ($)</label>
                                <input type="number" id="discount-fixed" class="w-full p-2 border rounded" placeholder="e.g. 2.50">
                            </div>
                            
                            <div id="bundle-discount" class="discount-input hidden">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <label class="block text-gray-700 mb-1">Buy</label>
                                        <input type="number" id="buy-quantity" class="w-full p-2 border rounded" placeholder="e.g. 5">
                                    </div>
                                    <div>
                                        <label class="block text-gray-700 mb-1">Get</label>
                                        <input type="number" id="get-quantity" class="w-full p-2 border rounded" placeholder="e.g. 1">
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <label class="block text-gray-700 mb-1">Type</label>
                                    <select id="bundle-type" class="w-full p-2 border rounded">
                                        <option value="free">Free</option>
                                        <option value="discount">Discount % on bundle</option>
                                    </select>
                                </div>
                                <div id="bundle-discount-percentage" class="mt-2 hidden">
                                    <label class="block text-gray-700 mb-1">Discount % for bundle</label>
                                    <input type="number" id="bundle-discount-value" class="w-full p-2 border rounded" placeholder="e.g. 15">
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <label class="block text-gray-700 mb-1">Minimum Quantity (for discount)</label>
                                <input type="number" id="min-quantity" class="w-full p-2 border rounded" placeholder="0 (no minimum)">
                            </div>
                        </div>
                    </div>
                    
                    <button id="add-to-list" class="w-full py-3 px-4 green-button rounded-lg font-bold transition duration-200 flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i> Add to List
                    </button>
                    
                    <div class="mt-6 border-t pt-6">
                        <h4 class="font-bold mb-4">Quick Stats</h4>
                        <div class="grid grid-cols-3 gap-2 text-center">
                            <div class="bg-blue-50 p-3 rounded">
                                <div class="text-sm text-blue-600">Total Items</div>
                                <div id="total-items" class="text-2xl font-bold">0</div>
                            </div>
                            <div class="bg-green-50 p-3 rounded">
                                <div class="text-sm text-green-600">Remaining</div>
                                <div id="remaining-items" class="text-2xl font-bold">0</div>
                            </div>
                            <div class="bg-purple-50 p-3 rounded">
                                <div class="text-sm text-purple-600">Total Cost</div>
                                <div id="total-cost" class="text-2xl font-bold">$0.00</div>
                            </div>
                        </div>
                        <div class="mt-2 bg-yellow-50 p-3 rounded">
                            <div class="text-sm text-yellow-600">Total Savings</div>
                            <div id="total-savings" class="text-xl font-bold">$0.00</div>
                        </div>
                    </div>
                    
                    <button id="print-grocery-bill" class="w-full mt-6 py-3 px-4 blue-button rounded-lg font-bold transition duration-200 flex items-center justify-center">
                        <i class="fas fa-print mr-2"></i> Print Grocery Bill
                    </button>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md md:col-span-2">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-green-600">Your Grocery List</h3>
                        <div>
                            <button id="clear-completed" class="px-3 py-1 bg-gray-200 text-gray-800 rounded-lg mr-1 text-sm">
                                Clear Completed
                            </button>
                            <button id="clear-all" class="px-3 py-1 bg-red-500 text-white rounded-lg text-sm">
                                Clear All
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex mb-4 space-x-2">
                        <select id="category-filter" class="p-2 border rounded-lg text-sm flex-1">
                            <option value="All Categories">All Categories</option>
                            <option value="Produce">Produce</option>
                            <option value="Dairy">Dairy</option>
                            <option value="Meat">Meat</option>
                            <option value="Bakery">Bakery</option>
                            <option value="Pantry">Pantry</option>
                            <option value="Frozen">Frozen</option>
                            <option value="Beverages">Beverages</option>
                            <option value="Household">Household</option>
                            <option value="Other">Other</option>
                        </select>
                        <select id="sort-by" class="p-2 border rounded-lg text-sm flex-1">
                            <option value="name">Sort by Name</option>
                            <option value="price">Sort by Price</option>
                            <option value="category">Sort by Category</option>
                            <option value="savings">Sort by Savings</option>
                        </select>
                    </div>
                    
                    <div id="grocery-items-container" class="border rounded-lg p-4 min-h-[320px]"> <div id="grocery-items" class="space-y-2">
                            <p id="empty-list-message" class="text-center text-gray-500 py-8">
                                Your grocery list is empty. Add some items to get started!
                            </p>
                            </div>
                    </div>
                    
                    <div class="mt-6 border-t pt-4">
                        <div class="flex border-b">
                            <button id="current-list-tab" class="px-4 py-2 border-b-2 border-green-600 text-green-600 font-medium">
                                Current List
                            </button>
                            <button id="purchase-history-tab" class="px-4 py-2 text-gray-500 font-medium">
                                Purchase History
                            </button>
                        </div>
                        
                        <div id="current-list-content" class="py-4">
                            </div>
                        
                        <div id="purchase-history-content" class="py-4 hidden">
                            <p id="empty-history-message" class="text-center text-gray-500 py-8">
                                No purchase history yet. Start shopping to see your history here!
                            </p>
                            <div id="purchase-history-items" class="space-y-3">
                                </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="grocery-bill-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center p-4 no-print">
                <div class="bg-white p-8 rounded-lg shadow-lg max-w-2xl w-full">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold">GROCERY BILL</h2>
                        <button id="close-bill-modal" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <div id="hotel-bill-content" class="hotel-bill"> <div class="hotel-header">
                            <div class="hotel-title">GROCERY MASTER</div>
                            <div class="hotel-subtitle">Your Shopping Receipt</div>
                            <div class="hotel-date" id="bill-date">Date: Loading...</div>
                        </div>
                        
                        <table class="hotel-items">
                            <thead>
                                <tr>
                                    <th class="item-name">Item</th>
                                    <th class="item-qty">Qty</th>
                                    <th class="item-price">Price</th>
                                    <th class="item-total">Total</th>
                                </tr>
                            </thead>
                            <tbody id="bill-items">
                                </tbody>
                        </table>
                        
                        <table class="hotel-totals">
                            <tr>
                                <td class="total-label">Subtotal:</td>
                                <td class="total-value" id="bill-subtotal">$0.00</td>
                            </tr>
                            <tr>
                                <td class="total-label">Savings:</td>
                                <td class="total-value hotel-savings" id="bill-savings">$0.00</td>
                            </tr>
                            <tr>
                                <td class="total-label">TOTAL:</td>
                                <td class="total-value" id="bill-total">$0.00</td>
                            </tr>
                        </table>
                        
                        <div class="hotel-footer">
                            <div>Thank you for shopping with us!</div>
                            <div class="mt-2">Generated by Smart Grocery List</div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-center">
                        <button id="print-bill" class="px-6 py-2 bg-blue-500 text-white rounded-lg">
                            <i class="fas fa-print mr-2"></i> Print
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="edit-item-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center p-4 no-print">
                <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold">Edit Item</h2>
                        <button id="close-edit-modal" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Item Name</label>
                        <input type="text" id="edit-item-name" class="w-full p-3 border rounded-lg">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-gray-700 mb-2">Price ($)</label>
                            <input type="number" id="edit-item-price" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Quantity</label>
                            <input type="number" id="edit-item-quantity" class="w-full p-3 border rounded-lg">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Category</label>
                        <select id="edit-item-category" class="w-full p-3 border rounded-lg">
                            <option value="Produce">Produce</option>
                            <option value="Dairy">Dairy</option>
                            <option value="Meat">Meat</option>
                            <option value="Bakery">Bakery</option>
                            <option value="Pantry">Pantry</option>
                            <option value="Frozen">Frozen</option>
                            <option value="Beverages">Beverages</option>
                            <option value="Household">Household</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Discount (optional)</label>
                        <select id="edit-discount-type" class="w-full p-3 border rounded-lg mb-2">
                            <option value="none">No discount</option>
                            <option value="percentage">Percentage discount</option>
                            <option value="fixed">Fixed amount discount</option>
                            <option value="bundle">Bundle discount (e.g. buy X get Y)</option>
                        </select>
                        
                        <div id="edit-discount-details" class="hidden">
                            <div id="edit-percentage-discount" class="discount-input hidden">
                                <label class="block text-gray-700 mb-1">Discount %</label>
                                <input type="number" id="edit-discount-percentage" class="w-full p-2 border rounded" placeholder="e.g. 15">
                            </div>
                            
                            <div id="edit-fixed-discount" class="discount-input hidden">
                                <label class="block text-gray-700 mb-1">Discount Amount ($)</label>
                                <input type="number" id="edit-discount-fixed" class="w-full p-2 border rounded" placeholder="e.g. 2.50">
                            </div>
                            
                            <div id="edit-bundle-discount" class="discount-input hidden">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <label class="block text-gray-700 mb-1">Buy</label>
                                        <input type="number" id="edit-buy-quantity" class="w-full p-2 border rounded" placeholder="e.g. 5">
                                    </div>
                                    <div>
                                        <label class="block text-gray-700 mb-1">Get</label>
                                        <input type="number" id="edit-get-quantity" class="w-full p-2 border rounded" placeholder="e.g. 1">
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <label class="block text-gray-700 mb-1">Type</label>
                                    <select id="edit-bundle-type" class="w-full p-2 border rounded">
                                        <option value="free">Free</option>
                                        <option value="discount">Discount % on bundle</option>
                                    </select>
                                </div>
                                <div id="edit-bundle-discount-percentage" class="mt-2 hidden">
                                    <label class="block text-gray-700 mb-1">Discount % for bundle</label>
                                    <input type="number" id="edit-bundle-discount-value" class="w-full p-2 border rounded" placeholder="e.g. 15">
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <label class="block text-gray-700 mb-1">Minimum Quantity (for discount)</label>
                                <input type="number" id="edit-min-quantity" class="w-full p-2 border rounded" placeholder="0 (no minimum)">
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-4 mt-6">
                        <button id="cancel-edit" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg">
                            Cancel
                        </button>
                        <button id="save-edit" class="px-4 py-2 bg-green-600 text-white rounded-lg">
                            Save Changes
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="neon-calc" class="tab-content">
            <div class="flex justify-center items-center py-6">
                <div class="calculator-container relative" style="width: 320px;">
                    <div class="calculator-power">
                        <i class="fas fa-power-off"></i>
                    </div>
                    
                    <div class="calculator-screen">
                        <div id="calc-display" class="text-right">0</div>
                    </div>
                    
                    <div class="grid grid-cols-4 gap-3">
                        <button class="calculator-button special p-4">AC</button>
                        <button class="calculator-button special p-4">CE</button>
                        <button class="calculator-button operator p-4">/</button>
                        <button class="calculator-button operator p-4">×</button>
                        
                        <button class="calculator-button p-4">7</button>
                        <button class="calculator-button p-4">8</button>
                        <button class="calculator-button p-4">9</button>
                        <button class="calculator-button operator p-4">-</button>
                        
                        <button class="calculator-button p-4">4</button>
                        <button class="calculator-button p-4">5</button>
                        <button class="calculator-button p-4">6</button>
                        <button class="calculator-button operator p-4">+</button>
                        
                        <button class="calculator-button p-4">1</button>
                        <button class="calculator-button p-4">2</button>
                        <button class="calculator-button p-4">3</button>
                        <button class="calculator-button operator p-4">±</button>
                        
                        <button class="calculator-button p-4">0</button>
                        <button class="calculator-button p-4">.</button>
                        <button class="calculator-button p-4">%</button>
                        <button class="calculator-button equals p-4">=</button>
                    </div>
                    
                    <div class="text-center mt-4 text-gray-400">
                        Neon Calculator v1.1 with History
                    </div>
                    
                    <div id="calc-history" class="mt-6 space-y-2 text-white text-sm max-h-24 overflow-y-auto">
                        </div>
                </div>
            </div>
        </div>

        <div id="age-calculator" class="tab-content">
            <div class="age-calculator-header p-6 rounded-lg shadow-md mb-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-2xl font-bold">Age Calculator</h2>
                        <p class="text-white text-opacity-90">Calculate your exact age in years, months and days</p>
                    </div>
                    <div>
                        <i class="fas fa-birthday-cake text-3xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-purple-600 mb-4">Enter Your Birth Date</h3>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Birth Date</label>
                        <input type="date" id="birth-date" class="w-full p-3 border rounded-lg date-input">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Compare With (Optional)</label>
                        <input type="date" id="compare-date" class="w-full p-3 border rounded-lg date-input">
                        <p class="text-sm text-gray-500 mt-1">Leave empty to compare with today's date</p>
                    </div>
                    
                    <button id="calculate-age" class="w-full py-3 px-4 bg-purple-600 text-white rounded-lg font-bold transition duration-200 flex items-center justify-center hover:bg-purple-700">
                        <i class="fas fa-calculator mr-2"></i> Calculate Age
                    </button>
                    
                    <div class="mt-6">
                        <h4 class="font-bold mb-2">Quick Options for Compare Date</h4>
                        <div class="grid grid-cols-2 gap-2">
                            <button id="today-button" class="py-2 px-3 bg-gray-100 rounded text-sm hover:bg-gray-200">
                                <i class="fas fa-calendar-day mr-1"></i> Today
                            </button>
                            <button id="yesterday-button" class="py-2 px-3 bg-gray-100 rounded text-sm hover:bg-gray-200">
                                <i class="fas fa-calendar-minus mr-1"></i> Yesterday
                            </button>
                            <button id="tomorrow-button" class="py-2 px-3 bg-gray-100 rounded text-sm hover:bg-gray-200">
                                <i class="fas fa-calendar-plus mr-1"></i> Tomorrow
                            </button>
                            <button id="clear-compare-date-button" class="py-2 px-3 bg-gray-100 rounded text-sm hover:bg-gray-200">
                                <i class="fas fa-eraser mr-1"></i> Clear Compare Date
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-purple-600 mb-4">Your Age</h3>
                    
                    <div id="age-result-display" class="age-result text-center mb-6 hidden"> <div class="grid grid-cols-3 gap-4">
                            <div>
                                <div class="age-value" id="years-value">0</div>
                                <div class="age-unit">Years</div>
                            </div>
                            <div>
                                <div class="age-value" id="months-value">0</div>
                                <div class="age-unit">Months</div>
                            </div>
                            <div>
                                <div class="age-value" id="days-value">0</div>
                                <div class="age-unit">Days</div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="age-details" class="hidden">
                        <div class="mb-4">
                            <h4 class="font-bold mb-2">Detailed Information</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="grid grid-cols-2 gap-4 mb-2">
                                    <div>
                                        <span class="text-gray-600">Birth Date:</span>
                                    </div>
                                    <div>
                                        <span id="birth-date-display" class="font-medium"></span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4 mb-2">
                                    <div>
                                        <span class="text-gray-600">Compared Date:</span>
                                    </div>
                                    <div>
                                        <span id="compare-date-display" class="font-medium"></span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <span class="text-gray-600">Total Days:</span>
                                    </div>
                                    <div>
                                        <span id="total-days" class="font-medium"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h4 class="font-bold mb-2">Next Birthday</h4>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <span class="text-purple-600">Next Birthday:</span>
                                    </div>
                                    <div>
                                        <span id="next-birthday" class="font-medium text-purple-600"></span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4 mt-2">
                                    <div>
                                        <span class="text-purple-600">Days Remaining:</span>
                                    </div>
                                    <div>
                                        <span id="days-remaining" class="font-medium text-purple-600"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h4 class="font-bold mb-2">Zodiac Sign</h4>
                            <div class="flex items-center">
                                <div id="zodiac-icon" class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-star text-yellow-500"></i>
                                </div>
                                <div>
                                    <div id="zodiac-sign" class="font-medium"></div>
                                    <div id="zodiac-dates" class="text-sm text-gray-500"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="age-error" class="bg-red-50 text-red-600 p-4 rounded-lg hidden">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span id="error-message"></span>
                    </div>
                    
                    <div id="age-placeholder" class="text-center py-12 text-gray-400">
                        <i class="fas fa-birthday-cake text-4xl mb-3"></i>
                        <p>Enter your birth date to calculate your age</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">Recent Calculations (Age)</h3>
                    <button id="clear-age-history" class="px-3 py-1 bg-gray-200 text-gray-800 rounded-lg text-sm hover:bg-gray-300">
                        <i class="fas fa-trash-alt mr-1"></i> Clear History
                    </button>
                </div>
                
                <div id="age-history-list" class="space-y-3"> <p id="empty-age-history" class="text-center text-gray-500 py-8">
                        No age calculations yet. Calculate your age to see history here!
                    </p>
                    </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- Tab switching functionality ---
            const tabsConfig = {
                'tab-grocery-calc': 'grocery-calculator',
                'tab-grocery-list': 'grocery-list',
                'tab-neon-calc': 'neon-calc', // Matched to div ID
                'tab-age-calc': 'age-calculator'
            };
            
            Object.keys(tabsConfig).forEach(tabId => {
                const tabButton = document.getElementById(tabId);
                if (tabButton) {
                    tabButton.addEventListener('click', function() {
                        // Remove active class from all tab content
                        document.querySelectorAll('.tab-content').forEach(content => {
                            content.classList.remove('active');
                        });
                        // Remove active class from all tab buttons
                        document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                            tab.classList.remove('active');
                        });
                        
                        // Add active class to clicked tab button and corresponding content
                        this.classList.add('active');
                        const contentDiv = document.getElementById(tabsConfig[tabId]);
                        if (contentDiv) {
                            contentDiv.classList.add('active');
                        }
                    });
                }
            });

            // --- Grocery Calculator Functionality ---
            const pricePerKgInput = document.getElementById('price-per-kg');
            const yourMoneyInput = document.getElementById('your-money');
            const desiredQuantityInput = document.getElementById('desired-quantity');
            const calculateQuantityBtn = document.getElementById('calculate-quantity');
            const calculateCostBtn = document.getElementById('calculate-cost');
            const resetCalcBtn = document.getElementById('reset-calc');
            const clearHistoryBtn = document.getElementById('clear-history'); // Grocery Calc History
            const calculationHistoryDiv = document.getElementById('calculation-history'); // Grocery Calc History display
            const historyCountSpan = document.getElementById('history-count'); // Grocery Calc History count

            let groceryCalcHistoryItems = []; // Renamed to avoid conflict

            function updateGroceryCalcHistoryDisplay() {
                if (!calculationHistoryDiv || !historyCountSpan) return;
                calculationHistoryDiv.innerHTML = '';
                groceryCalcHistoryItems.forEach(item => {
                    const historyItemEl = document.createElement('div');
                    historyItemEl.className = 'history-item p-4 rounded-lg'; // Uses shared style
                    
                    const iconClass = item.type === 'money' ? 'fas fa-coins text-blue-500' : 'fas fa-weight text-green-500';
                    const bgClass = item.type === 'money' ? 'bg-blue-100' : 'bg-green-100';
                    
                    let content = '';
                    if (item.type === 'money') {
                        content = `
                            <div class="flex items-center">
                                <div class="w-8 h-8 ${bgClass} rounded-full flex items-center justify-center mr-3">
                                    <i class="${iconClass}"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between">
                                        <div>₹${item.money} → ${item.quantity.toFixed(2)}g</div>
                                        <div class="text-gray-500 text-sm">${item.time}</div>
                                    </div>
                                    <div class="text-sm text-gray-500">@ ₹${item.rate}/kg</div>
                                </div>
                            </div>
                        `;
                    } else {
                        content = `
                            <div class="flex items-center">
                                <div class="w-8 h-8 ${bgClass} rounded-full flex items-center justify-center mr-3">
                                    <i class="${iconClass}"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between">
                                        <div>${item.quantity}g → ₹${item.cost.toFixed(2)}</div>
                                        <div class="text-gray-500 text-sm">${item.time}</div>
                                    </div>
                                    <div class="text-sm text-gray-500">@ ₹${item.rate}/kg</div>
                                </div>
                            </div>
                        `;
                    }
                    historyItemEl.innerHTML = content;
                    calculationHistoryDiv.appendChild(historyItemEl);
                });
                historyCountSpan.textContent = `${groceryCalcHistoryItems.length} items`;
            }
            
            function getCurrentTime() {
                const now = new Date();
                let hours = now.getHours();
                const minutes = now.getMinutes();
                const ampm = hours >= 12 ? 'PM' : 'AM';
                hours = hours % 12;
                hours = hours ? hours : 12; // Handle midnight
                const minutesStr = minutes < 10 ? '0' + minutes : minutes;
                return `${hours}:${minutesStr} ${ampm}`;
            }

            if (calculateQuantityBtn) {
                calculateQuantityBtn.addEventListener('click', function() {
                    const pricePerKg = parseFloat(pricePerKgInput.value) || 0;
                    const yourMoney = parseFloat(yourMoneyInput.value) || 0;
                    
                    if (pricePerKg <= 0) {
                        alert('Please enter a valid price per kg');
                        return;
                    }
                    if (yourMoney <= 0) {
                        alert('Please enter a valid amount of money');
                        return;
                    }
                    
                    const quantity = (yourMoney / pricePerKg) * 1000;
                    
                    const calculation = {
                        type: 'money',
                        money: yourMoney,
                        quantity: quantity,
                        rate: pricePerKg,
                        time: getCurrentTime()
                    };
                    
                    groceryCalcHistoryItems.unshift(calculation);
                    if(groceryCalcHistoryItems.length > 10) groceryCalcHistoryItems.pop(); // Limit history
                    updateGroceryCalcHistoryDisplay();
                    // alert(`₹${yourMoney} will get you ${quantity.toFixed(2)} grams at ₹${pricePerKg}/kg`);
                });
            }

            if (calculateCostBtn) {
                calculateCostBtn.addEventListener('click', function() {
                    const pricePerKg = parseFloat(pricePerKgInput.value) || 0; // Use the same price per kg
                    const quantity = parseFloat(desiredQuantityInput.value) || 0;
                    
                    if (pricePerKg <= 0) {
                        alert('Please enter a valid price per kg (in the Money to Quantity section)');
                        return;
                    }
                     if (quantity <= 0) {
                        alert('Please enter a valid desired quantity');
                        return;
                    }
                    
                    const cost = (quantity / 1000) * pricePerKg;
                    
                    const calculation = {
                        type: 'quantity',
                        quantity: quantity,
                        cost: cost,
                        rate: pricePerKg,
                        time: getCurrentTime()
                    };
                    
                    groceryCalcHistoryItems.unshift(calculation);
                    if(groceryCalcHistoryItems.length > 10) groceryCalcHistoryItems.pop(); // Limit history
                    updateGroceryCalcHistoryDisplay();
                    // alert(`${quantity} grams will cost ₹${cost.toFixed(2)} at ₹${pricePerKg}/kg`);
                });
            }

            if (resetCalcBtn) {
                resetCalcBtn.addEventListener('click', function() {
                    if(pricePerKgInput) pricePerKgInput.value = '1000';
                    if(yourMoneyInput) yourMoneyInput.value = '';
                    if(desiredQuantityInput) desiredQuantityInput.value = '';
                });
            }

            if (clearHistoryBtn) {
                clearHistoryBtn.addEventListener('click', function() {
                    groceryCalcHistoryItems = [];
                    updateGroceryCalcHistoryDisplay();
                });
            }
            updateGroceryCalcHistoryDisplay(); // Initial call


            // --- Grocery List Functionality ---
            const itemNameInput = document.getElementById('item-name');
            const itemPriceInput = document.getElementById('item-price');
            const itemQuantityInput = document.getElementById('item-quantity');
            const itemCategorySelect = document.getElementById('item-category');
            const addToListBtn = document.getElementById('add-to-list');
            const groceryItemsContainer = document.getElementById('grocery-items');
            const emptyListMessage = document.getElementById('empty-list-message');
            const totalItemsElement = document.getElementById('total-items');
            const remainingItemsElement = document.getElementById('remaining-items');
            const totalCostElement = document.getElementById('total-cost');
            const totalSavingsElement = document.getElementById('total-savings');
            const categoryFilterSelect = document.getElementById('category-filter');
            const sortBySelect = document.getElementById('sort-by');
            const clearCompletedBtn = document.getElementById('clear-completed');
            const clearAllBtn = document.getElementById('clear-all');
            
            const currentListTab = document.getElementById('current-list-tab');
            const purchaseHistoryTab = document.getElementById('purchase-history-tab');
            const currentListContent = document.getElementById('current-list-content'); // This div might be conceptual
            const purchaseHistoryContent = document.getElementById('purchase-history-content');
            const purchaseHistoryItemsDiv = document.getElementById('purchase-history-items');
            const emptyHistoryMessageDiv = document.getElementById('empty-history-message');

            const printGroceryBillBtn = document.getElementById('print-grocery-bill');
            const groceryBillModal = document.getElementById('grocery-bill-modal');
            const closeBillModalBtn = document.getElementById('close-bill-modal');
            const billItemsContainer = document.getElementById('bill-items');
            const billTotalElement = document.getElementById('bill-total');
            const billSubtotalElement = document.getElementById('bill-subtotal');
            const billSavingsElement = document.getElementById('bill-savings');
            const printBillBtn = document.getElementById('print-bill');
            const billDateElement = document.getElementById('bill-date');
            
            // Edit modal elements
            const editItemModal = document.getElementById('edit-item-modal');
            const closeEditModalBtn = document.getElementById('close-edit-modal');
            const cancelEditBtn = document.getElementById('cancel-edit');
            const saveEditBtn = document.getElementById('save-edit');
            const editItemNameInput = document.getElementById('edit-item-name');
            const editItemPriceInput = document.getElementById('edit-item-price');
            const editItemQuantityInput = document.getElementById('edit-item-quantity');
            const editItemCategorySelect = document.getElementById('edit-item-category');
            const editDiscountTypeSelect = document.getElementById('edit-discount-type');
            const editDiscountDetailsDiv = document.getElementById('edit-discount-details');
            const editPercentageDiscountDiv = document.getElementById('edit-percentage-discount');
            const editFixedDiscountDiv = document.getElementById('edit-fixed-discount');
            const editBundleDiscountDiv = document.getElementById('edit-bundle-discount');
            const editBuyQuantityInput = document.getElementById('edit-buy-quantity');
            const editGetQuantityInput = document.getElementById('edit-get-quantity');
            const editBundleTypeSelect = document.getElementById('edit-bundle-type');
            const editBundleDiscountPercentageDiv = document.getElementById('edit-bundle-discount-percentage');
            const editBundleDiscountValueInput = document.getElementById('edit-bundle-discount-value');
            const editMinQuantityInput = document.getElementById('edit-min-quantity');
            
            // Discount elements (for adding new item)
            const discountTypeSelect = document.getElementById('discount-type');
            const discountDetailsDiv = document.getElementById('discount-details');
            const percentageDiscountDiv = document.getElementById('percentage-discount');
            const discountPercentageInput = document.getElementById('discount-percentage');
            const fixedDiscountDiv = document.getElementById('fixed-discount');
            const discountFixedInput = document.getElementById('discount-fixed');
            const bundleDiscountDiv = document.getElementById('bundle-discount');
            const buyQuantityInput = document.getElementById('buy-quantity');
            const getQuantityInput = document.getElementById('get-quantity');
            const bundleTypeSelect = document.getElementById('bundle-type');
            const bundleDiscountPercentageDiv = document.getElementById('bundle-discount-percentage');
            const bundleDiscountValueInput = document.getElementById('bundle-discount-value');
            const minQuantityInput = document.getElementById('min-quantity');

            let groceryItems = JSON.parse(localStorage.getItem('groceryItems')) || [];
            let purchaseHistory = JSON.parse(localStorage.getItem('purchaseHistory')) || [];
            let currentlyEditingIndex = -1;

            function saveGroceryData() {
                localStorage.setItem('groceryItems', JSON.stringify(groceryItems));
                localStorage.setItem('purchaseHistory', JSON.stringify(purchaseHistory));
            }

            function setupDiscountFields(typeSelect, detailsDiv, percentageDiv, fixedDiv, bundleDiv, bundleTypeSel, bundlePercentageDiv) {
                if (!typeSelect) return;
                typeSelect.addEventListener('change', function() {
                    const type = this.value;
                    
                    detailsDiv.classList.add('hidden');
                    percentageDiv.classList.add('hidden');
                    fixedDiv.classList.add('hidden');
                    bundleDiv.classList.add('hidden');
                    if (bundlePercentageDiv) bundlePercentageDiv.classList.add('hidden');
                    
                    if (type !== 'none') {
                        detailsDiv.classList.remove('hidden');
                        if (type === 'percentage') percentageDiv.classList.remove('hidden');
                        else if (type === 'fixed') fixedDiv.classList.remove('hidden');
                        else if (type === 'bundle') {
                            bundleDiv.classList.remove('hidden');
                            if (bundleTypeSel && bundleTypeSel.value === 'discount' && bundlePercentageDiv) {
                                bundlePercentageDiv.classList.remove('hidden');
                            }
                        }
                    }
                });
                // Trigger change for initial state
                typeSelect.dispatchEvent(new Event('change'));

                if (bundleTypeSel && bundlePercentageDiv) {
                    bundleTypeSel.addEventListener('change', function() {
                        if (this.value === 'discount') {
                            bundlePercentageDiv.classList.remove('hidden');
                        } else {
                            bundlePercentageDiv.classList.add('hidden');
                        }
                    });
                     // Trigger change for initial state
                    bundleTypeSel.dispatchEvent(new Event('change'));
                }
            }

            // Setup for Add Item form
            setupDiscountFields(discountTypeSelect, discountDetailsDiv, percentageDiscountDiv, fixedDiscountDiv, bundleDiscountDiv, bundleTypeSelect, bundleDiscountPercentageDiv);
            // Setup for Edit Item form
            setupDiscountFields(editDiscountTypeSelect, editDiscountDetailsDiv, editPercentageDiscountDiv, editFixedDiscountDiv, editBundleDiscountDiv, editBundleTypeSelect, editBundleDiscountPercentageDiv);


            function calculateItemCost(item) {
                let originalTotalCost = item.price * item.quantity;
                let finalCost = originalTotalCost;
                let savings = 0;
                let discountApplied = false;
                let discountDescription = '';

                if (item.discount && item.discount.type !== 'none' && item.quantity >= (item.discount.minQuantity || 0)) {
                    discountApplied = true;
                    switch (item.discount.type) {
                        case 'percentage':
                            savings = originalTotalCost * (item.discount.percentage / 100);
                            discountDescription = `${item.discount.percentage}% off`;
                            break;
                        case 'fixed':
                            // Apply fixed discount per item, up to the item's price
                            const singleItemOriginalPrice = item.price;
                            const singleItemFixedDiscount = Math.min(item.discount.fixed, singleItemOriginalPrice);
                            savings = singleItemFixedDiscount * item.quantity;
                            discountDescription = `$${item.discount.fixed.toFixed(2)} off each`;
                            break;
                        case 'bundle':
                            const buy = item.discount.buy;
                            const get = item.discount.get;
                            const numBundles = Math.floor(item.quantity / buy);

                            if (numBundles > 0) {
                                if (item.discount.bundleType === 'free') {
                                    // Calculate how many 'get' items are effectively free
                                    // This logic assumes 'get' items are part of the total quantity
                                    // A clearer model might be "buy X, get Y free" meaning X+Y items for price of X
                                    // For simplicity here: if you buy 'buy' items, 'get' items become free if they are part of the quantity
                                    // A more robust bundle: Buy X, get Y free. If quantity is X+Y, cost is X*price.
                                    // Let's assume for 'buy X get Y free', if you have X+Y items, Y are free.
                                    // Number of full "buy X get Y" sets
                                    const fullSets = Math.floor(item.quantity / (buy + get));
                                    savings = fullSets * get * item.price;
                                    discountDescription = `Buy ${buy} get ${get} free`;

                                } else if (item.discount.bundleType === 'discount') {
                                    // Discount applies to 'buy' items in the bundle
                                    const itemsInDiscountableBundles = numBundles * buy;
                                    const costOfDiscountableItems = itemsInDiscountableBundles * item.price;
                                    savings = costOfDiscountableItems * (item.discount.bundleDiscount / 100);
                                    discountDescription = `${item.discount.bundleDiscount}% off on ${buy} items per bundle`;
                                }
                            } else {
                                discountApplied = false; // Not enough quantity for bundle
                            }
                            break;
                        default:
                            discountApplied = false;
                    }
                    finalCost = originalTotalCost - savings;
                }
                 // Ensure savings don't make cost negative
                if (savings > originalTotalCost) savings = originalTotalCost;
                finalCost = originalTotalCost - savings;


                return {
                    totalCost: Math.max(0, finalCost), // Ensure cost is not negative
                    savings: savings,
                    discountApplied: discountApplied,
                    discountDescription: discountDescription
                };
            }
            
            function updateGroceryList() {
                if (!groceryItemsContainer || !emptyListMessage) return;

                const selectedCategory = categoryFilterSelect ? categoryFilterSelect.value : 'All Categories';
                const sortBy = sortBySelect ? sortBySelect.value : 'name';
                
                let filteredItems = [...groceryItems];
                
                if (selectedCategory !== 'All Categories') {
                    filteredItems = filteredItems.filter(item => item.category === selectedCategory);
                }
                
                filteredItems.sort((a, b) => {
                    const aCostInfo = calculateItemCost(a);
                    const bCostInfo = calculateItemCost(b);
                    if (sortBy === 'name') return a.name.localeCompare(b.name);
                    if (sortBy === 'price') return aCostInfo.totalCost - bCostInfo.totalCost; // Sort by final price
                    if (sortBy === 'category') return a.category.localeCompare(b.category);
                    if (sortBy === 'savings') return bCostInfo.savings - aCostInfo.savings;
                    return 0;
                });
                
                groceryItemsContainer.innerHTML = '';
                
                if (filteredItems.length === 0 && groceryItems.length === 0) { // Show message only if main list is empty
                    emptyListMessage.style.display = 'block';
                } else if (filteredItems.length === 0 && groceryItems.length > 0) {
                     emptyListMessage.style.display = 'block';
                     emptyListMessage.textContent = 'No items match current filter.';
                }
                else {
                    emptyListMessage.style.display = 'none';
                    filteredItems.forEach((item) => { // Use original index from groceryItems for actions
                        const originalIndex = groceryItems.findIndex(gi => gi.id === item.id); // Requires item IDs

                        const itemElement = document.createElement('div');
                        itemElement.className = `p-3 border rounded-lg flex items-center justify-between ${item.completed ? 'bg-gray-100 opacity-70' : 'bg-white'}`;
                        
                        const costInfo = calculateItemCost(item);
                        const displayPrice = costInfo.totalCost.toFixed(2);
                        
                        let discountBadge = '';
                        if (costInfo.discountApplied && costInfo.discountDescription) {
                            discountBadge = `<span class="discount-badge">${costInfo.discountDescription}</span>`;
                        }
                        
                        itemElement.innerHTML = `
                            <div class="flex items-center flex-grow">
                                <input type="checkbox" class="mr-3 h-5 w-5 flex-shrink-0" 
                                    ${item.completed ? 'checked' : ''} data-id="${item.id}">
                                <div class="flex-grow">
                                    <div class="${item.completed ? 'line-through text-gray-500' : ''}">
                                        ${item.name} ${discountBadge}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        ${item.category} &bull; $${item.price.toFixed(2)} &times; ${item.quantity} = $${displayPrice}
                                        ${costInfo.savings > 0 ? `<span class="text-green-600 ml-2">(Saved $${costInfo.savings.toFixed(2)})</span>` : ''}
                                    </div>
                                </div>
                            </div>
                            <div class="flex space-x-2 flex-shrink-0 ml-2">
                                <button class="text-blue-500 edit-item" data-id="${item.id}" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-500 delete-item" data-id="${item.id}" title="Delete">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        `;
                        groceryItemsContainer.appendChild(itemElement);
                    });
                }
                 // Add event listeners after items are rendered
                addGroceryItemEventListeners();
                updateStats();
                saveGroceryData();
            }

            function addGroceryItemEventListeners() {
                document.querySelectorAll('#grocery-items-container input[type="checkbox"]').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const itemId = this.dataset.id;
                        const itemIndex = groceryItems.findIndex(item => item.id === itemId);
                        if (itemIndex > -1) {
                            groceryItems[itemIndex].completed = this.checked;
                            updateGroceryList();
                        }
                    });
                });

                document.querySelectorAll('#grocery-items-container .edit-item').forEach(button => {
                    button.addEventListener('click', function() {
                        const itemId = this.dataset.id;
                        const itemIndex = groceryItems.findIndex(item => item.id === itemId);
                        if (itemIndex > -1) {
                             openEditModal(itemIndex);
                        }
                    });
                });

                document.querySelectorAll('#grocery-items-container .delete-item').forEach(button => {
                    button.addEventListener('click', function() {
                        const itemId = this.dataset.id;
                        groceryItems = groceryItems.filter(item => item.id !== itemId);
                        updateGroceryList();
                    });
                });
            }
            
            function openEditModal(index) {
                if (index < 0 || index >= groceryItems.length || !editItemModal) return;
                
                const item = groceryItems[index];
                currentlyEditingIndex = index;
                
                editItemNameInput.value = item.name;
                editItemPriceInput.value = item.price;
                editItemQuantityInput.value = item.quantity;
                editItemCategorySelect.value = item.category;
                
                // Reset discount fields before populating
                editDiscountTypeSelect.value = 'none';
                editDiscountDetailsDiv.classList.add('hidden');
                editPercentageDiscountDiv.classList.add('hidden');
                editFixedDiscountDiv.classList.add('hidden');
                editBundleDiscountDiv.classList.add('hidden');
                editBundleDiscountPercentageDiv.classList.add('hidden');


                if (item.discount && item.discount.type !== 'none') {
                    editDiscountTypeSelect.value = item.discount.type;
                    if (item.discount.type === 'percentage') {
                        document.getElementById('edit-discount-percentage').value = item.discount.percentage || '';
                    } else if (item.discount.type === 'fixed') {
                        document.getElementById('edit-discount-fixed').value = item.discount.fixed || '';
                    } else if (item.discount.type === 'bundle') {
                        editBuyQuantityInput.value = item.discount.buy || '';
                        editGetQuantityInput.value = item.discount.get || '';
                        editBundleTypeSelect.value = item.discount.bundleType || 'free';
                        if (item.discount.bundleType === 'discount') {
                           editBundleDiscountValueInput.value = item.discount.bundleDiscount || '';
                        }
                    }
                    editMinQuantityInput.value = item.discount.minQuantity || 0;
                }
                
                // Trigger change events to show/hide correct fields
                editDiscountTypeSelect.dispatchEvent(new Event('change'));
                if (editBundleTypeSelect && item.discount && item.discount.type === 'bundle') {
                     editBundleTypeSelect.dispatchEvent(new Event('change'));
                }
                
                editItemModal.classList.remove('hidden');
            }
            
            function updateStats() {
                if (!totalItemsElement || !remainingItemsElement || !totalCostElement || !totalSavingsElement) return;

                const totalItems = groceryItems.length;
                const remainingItems = groceryItems.filter(item => !item.completed).length;
                let currentTotalCost = 0;
                let currentTotalSavings = 0;
                
                groceryItems.forEach(item => {
                    if (!item.completed) { // Calculate cost/savings only for non-completed items for current list stats
                        const costInfo = calculateItemCost(item);
                        currentTotalCost += costInfo.totalCost;
                        currentTotalSavings += costInfo.savings;
                    }
                });
                
                totalItemsElement.textContent = totalItems;
                remainingItemsElement.textContent = remainingItems;
                totalCostElement.textContent = `$${currentTotalCost.toFixed(2)}`;
                totalSavingsElement.textContent = `$${currentTotalSavings.toFixed(2)}`;
            }

            if (addToListBtn) {
                addToListBtn.addEventListener('click', function() {
                    const name = itemNameInput.value.trim();
                    const price = parseFloat(itemPriceInput.value) || 0;
                    const quantity = parseInt(itemQuantityInput.value) || 1;
                    const category = itemCategorySelect.value;
                    const selectedDiscountType = discountTypeSelect.value;
                    
                    if (!name) { alert('Please enter an item name'); return; }
                    if (price <= 0) { alert('Please enter a valid price'); return; }
                    if (quantity <= 0) { alert('Please enter a valid quantity'); return; }

                    let discount = { type: 'none' };
                    if (selectedDiscountType !== 'none') {
                        discount.type = selectedDiscountType;
                        discount.minQuantity = parseInt(minQuantityInput.value) || 0;
                        if (selectedDiscountType === 'percentage') {
                            discount.percentage = parseFloat(discountPercentageInput.value) || 0;
                            if (discount.percentage <= 0 || discount.percentage > 100) { alert('Invalid percentage'); return; }
                        } else if (selectedDiscountType === 'fixed') {
                            discount.fixed = parseFloat(discountFixedInput.value) || 0;
                            if (discount.fixed <= 0) { alert('Invalid fixed discount'); return; }
                        } else if (selectedDiscountType === 'bundle') {
                            discount.buy = parseInt(buyQuantityInput.value) || 0;
                            discount.get = parseInt(getQuantityInput.value) || 0;
                            discount.bundleType = bundleTypeSelect.value;
                            if (discount.buy <=0 || discount.get < 0) { alert('Invalid bundle quantities'); return;}
                            if (discount.bundleType === 'discount') {
                                discount.bundleDiscount = parseFloat(bundleDiscountValueInput.value) || 0;
                                if (discount.bundleDiscount <=0 || discount.bundleDiscount > 100) { alert('Invalid bundle discount %'); return;}
                            }
                        }
                    }
                    
                    groceryItems.push({
                        id: Date.now().toString(), // Simple unique ID
                        name, price, quantity, category, discount,
                        completed: false, date: new Date().toISOString()
                    });
                    
                    itemNameInput.value = ''; itemPriceInput.value = ''; itemQuantityInput.value = '1';
                    discountTypeSelect.value = 'none'; 
                    discountTypeSelect.dispatchEvent(new Event('change')); // Reset discount form
                    
                    updateGroceryList();
                });
            }
            
            if (saveEditBtn) {
                saveEditBtn.addEventListener('click', function() {
                    if (currentlyEditingIndex < 0 || currentlyEditingIndex >= groceryItems.length) return;
                    
                    const itemToEdit = groceryItems[currentlyEditingIndex];
                    const name = editItemNameInput.value.trim();
                    const price = parseFloat(editItemPriceInput.value) || 0;
                    const quantity = parseInt(editItemQuantityInput.value) || 1;
                    const category = editItemCategorySelect.value;
                    const selectedDiscountType = editDiscountTypeSelect.value;

                    if (!name) { alert('Please enter an item name'); return; }
                    if (price <= 0) { alert('Please enter a valid price'); return; }
                    if (quantity <= 0) { alert('Please enter a valid quantity'); return; }

                    let discount = { type: 'none' };
                     if (selectedDiscountType !== 'none') {
                        discount.type = selectedDiscountType;
                        discount.minQuantity = parseInt(editMinQuantityInput.value) || 0;
                        if (selectedDiscountType === 'percentage') {
                            discount.percentage = parseFloat(document.getElementById('edit-discount-percentage').value) || 0;
                             if (discount.percentage <= 0 || discount.percentage > 100) { alert('Invalid percentage'); return; }
                        } else if (selectedDiscountType === 'fixed') {
                            discount.fixed = parseFloat(document.getElementById('edit-discount-fixed').value) || 0;
                            if (discount.fixed <= 0) { alert('Invalid fixed discount'); return; }
                        } else if (selectedDiscountType === 'bundle') {
                            discount.buy = parseInt(editBuyQuantityInput.value) || 0;
                            discount.get = parseInt(editGetQuantityInput.value) || 0;
                            discount.bundleType = editBundleTypeSelect.value;
                            if (discount.buy <=0 || discount.get < 0) { alert('Invalid bundle quantities'); return;}
                            if (discount.bundleType === 'discount') {
                                discount.bundleDiscount = parseFloat(editBundleDiscountValueInput.value) || 0;
                                if (discount.bundleDiscount <=0 || discount.bundleDiscount > 100) { alert('Invalid bundle discount %'); return;}
                            }
                        }
                    }
                    
                    groceryItems[currentlyEditingIndex] = {
                        ...itemToEdit, // Preserve ID, completed status, date
                        name, price, quantity, category, discount
                    };
                    
                    if(editItemModal) editItemModal.classList.add('hidden');
                    updateGroceryList();
                });
            }

            if(closeEditModalBtn) closeEditModalBtn.addEventListener('click', () => editItemModal.classList.add('hidden'));
            if(cancelEditBtn) cancelEditBtn.addEventListener('click', () => editItemModal.classList.add('hidden'));
            
            if(categoryFilterSelect) categoryFilterSelect.addEventListener('change', updateGroceryList);
            if(sortBySelect) sortBySelect.addEventListener('change', updateGroceryList);
            
            if(clearCompletedBtn) {
                clearCompletedBtn.addEventListener('click', function() {
                    groceryItems = groceryItems.filter(item => !item.completed);
                    updateGroceryList();
                });
            }
            if(clearAllBtn) {
                clearAllBtn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to clear all items from the list?')) {
                        groceryItems = [];
                        updateGroceryList();
                    }
                });
            }

            if (currentListTab && purchaseHistoryTab && currentListContent && purchaseHistoryContent) {
                currentListTab.addEventListener('click', function() {
                    // currentListContent.classList.remove('hidden'); // Content is always visible via groceryItemsContainer
                    if(groceryItemsContainer) groceryItemsContainer.classList.remove('hidden');
                    if(emptyListMessage) emptyListMessage.classList.remove('hidden');


                    purchaseHistoryContent.classList.add('hidden');
                    currentListTab.classList.add('border-b-2', 'border-green-600', 'text-green-600');
                    purchaseHistoryTab.classList.remove('border-b-2', 'border-green-600', 'text-green-600');
                    purchaseHistoryTab.classList.add('text-gray-500');
                    updateGroceryList(); // Refresh current list view
                });
            
                purchaseHistoryTab.addEventListener('click', function() {
                    // currentListContent.classList.add('hidden');
                     if(groceryItemsContainer) groceryItemsContainer.classList.add('hidden');
                     if(emptyListMessage) emptyListMessage.classList.add('hidden');


                    purchaseHistoryContent.classList.remove('hidden');
                    purchaseHistoryTab.classList.add('border-b-2', 'border-green-600', 'text-green-600');
                    currentListTab.classList.remove('border-b-2', 'border-green-600', 'text-green-600');
                    currentListTab.classList.add('text-gray-500');
                    updatePurchaseHistoryDisplay(); // Show purchase history
                });
            }
            
            if (printGroceryBillBtn) {
                printGroceryBillBtn.addEventListener('click', function() {
                    const itemsToPrint = groceryItems.filter(item => !item.completed);
                    if (itemsToPrint.length === 0) {
                        alert('Your active grocery list is empty or all items are completed. Add some items first.');
                        return;
                    }
                    
                    const now = new Date();
                    const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
                    if(billDateElement) billDateElement.textContent = `Date: ${now.toLocaleDateString('en-US', options)}`;
                    
                    if(billItemsContainer) billItemsContainer.innerHTML = '';
                    let subtotal = 0;
                    let totalOverallSavings = 0;
                    
                    itemsToPrint.forEach(item => {
                        const costInfo = calculateItemCost(item);
                        const itemOriginalTotal = item.price * item.quantity;
                        subtotal += itemOriginalTotal; // Subtotal is sum of original prices before discount
                        totalOverallSavings += costInfo.savings;
                        
                        const billItem = document.createElement('tr');
                        let discountInfoHtml = '';
                        if (costInfo.discountApplied && costInfo.discountDescription) {
                            discountInfoHtml = `<div class="text-xs text-green-600">${costInfo.discountDescription}</div>`;
                        }
                        
                        billItem.innerHTML = `
                            <td class="item-name">${item.name}${discountInfoHtml}</td>
                            <td class="item-qty">${item.quantity}</td>
                            <td class="item-price">$${item.price.toFixed(2)}</td>
                            <td class="item-total">$${costInfo.totalCost.toFixed(2)}</td>
                        `;
                        if(billItemsContainer) billItemsContainer.appendChild(billItem);
                    });
                    
                    const finalTotal = subtotal - totalOverallSavings;
                    
                    if(billSubtotalElement) billSubtotalElement.textContent = `$${subtotal.toFixed(2)}`;
                    if(billSavingsElement) billSavingsElement.textContent = `$${totalOverallSavings.toFixed(2)}`;
                    if(billTotalElement) billTotalElement.textContent = `$${finalTotal.toFixed(2)}`;
                    
                    if(groceryBillModal) groceryBillModal.classList.remove('hidden');
                });
            }

            if(closeBillModalBtn) closeBillModalBtn.addEventListener('click', () => groceryBillModal.classList.add('hidden'));
            
            if(printBillBtn) {
                printBillBtn.addEventListener('click', function() {
                    const itemsPurchased = groceryItems.filter(item => !item.completed);
                    if (itemsPurchased.length > 0) {
                        let purchaseSubtotal = 0;
                        let purchaseTotalSavings = 0;
                        itemsPurchased.forEach(item => {
                            const costInfo = calculateItemCost(item);
                            purchaseSubtotal += item.price * item.quantity;
                            purchaseTotalSavings += costInfo.savings;
                        });

                        const purchase = {
                            id: Date.now().toString(),
                            date: new Date().toISOString(),
                            items: JSON.parse(JSON.stringify(itemsPurchased)), // Deep copy
                            total: purchaseSubtotal - purchaseTotalSavings,
                            savings: purchaseTotalSavings
                        };
                        purchaseHistory.unshift(purchase);
                        if(purchaseHistory.length > 20) purchaseHistory.pop(); // Limit history
                        
                        // Mark items as completed and remove from active list or clear list
                        groceryItems = groceryItems.filter(item => item.completed); 
                    }
                    
                    window.print(); // This will print the modal content due to @media print styles
                    if(groceryBillModal) groceryBillModal.classList.add('hidden'); // Hide modal after print dialog
                    updateGroceryList(); // Refresh list (items marked completed or removed)
                    updatePurchaseHistoryDisplay(); // Refresh history tab
                    saveGroceryData();
                });
            }

            function updatePurchaseHistoryDisplay() {
                if (!purchaseHistoryItemsDiv || !emptyHistoryMessageDiv) return;
                
                if (purchaseHistory.length === 0) {
                    emptyHistoryMessageDiv.style.display = 'block';
                    purchaseHistoryItemsDiv.innerHTML = '';
                    return;
                }
                
                emptyHistoryMessageDiv.style.display = 'none';
                purchaseHistoryItemsDiv.innerHTML = '';
                
                purchaseHistory.forEach(purchase => {
                    const date = new Date(purchase.date);
                    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
                    const formattedDate = date.toLocaleDateString('en-US', options);
                    
                    const historyItemEl = document.createElement('div');
                    historyItemEl.className = 'border rounded-lg p-4 bg-gray-50';
                    
                    let itemsSummary = purchase.items.slice(0, 2).map(it => it.name).join(', ');
                    if (purchase.items.length > 2) itemsSummary += ` and ${purchase.items.length - 2} more`;

                    historyItemEl.innerHTML = `
                        <div class="flex justify-between items-center mb-2">
                            <div class="font-bold">${formattedDate}</div>
                            <div class="text-xl font-bold text-green-700">$${purchase.total.toFixed(2)}</div>
                        </div>
                        <div class="text-sm text-gray-600">${purchase.items.length} items: ${itemsSummary}</div>
                        ${purchase.savings > 0 ? `<div class="text-sm text-blue-600 mt-1">You saved $${purchase.savings.toFixed(2)}!</div>` : ''}
                    `;
                    purchaseHistoryItemsDiv.appendChild(historyItemEl);
                });
                saveGroceryData();
            }
            
            updateGroceryList(); // Initial render of grocery list
            updatePurchaseHistoryDisplay(); // Initial render of purchase history


            // --- Neon Calculator Functionality ---
            const calcDisplay = document.getElementById('calc-display');
            const calcButtons = document.querySelectorAll('#neon-calc .calculator-button'); // Scoped to neon-calc
            const calcHistoryDiv = document.getElementById('calc-history'); // Neon calc history display

            let currentInput = '0';
            let previousInput = '';
            let operation = null;
            let resetInputNext = false; // Renamed to avoid conflict
            let neonCalcHistoryItems = []; // Renamed

            function updateNeonCalcDisplay() {
                if(calcDisplay) calcDisplay.textContent = currentInput;
            }

            function updateNeonCalcHistory() {
                if (!calcHistoryDiv) return;
                calcHistoryDiv.innerHTML = '';
                neonCalcHistoryItems.slice(0, 3).forEach(item => { // Show last 3
                    const historyItemEl = document.createElement('div');
                    historyItemEl.className = 'text-gray-400 p-1 rounded text-xs text-right'; // Adjusted style
                    historyItemEl.textContent = `${item.expression} = ${item.result}`;
                    calcHistoryDiv.appendChild(historyItemEl);
                });
            }

            if (calcButtons.length > 0) {
                calcButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const value = this.textContent;
                        
                        if (value === 'AC') {
                            currentInput = '0'; previousInput = ''; operation = null; resetInputNext = false;
                        } else if (value === 'CE') {
                            currentInput = '0'; resetInputNext = false;
                        } else if (value === '=') {
                            if (operation && previousInput !== '') {
                                const num1 = parseFloat(previousInput);
                                const num2 = parseFloat(currentInput);
                                let result;
                                const expression = `${previousInput} ${operation} ${currentInput}`;

                                if (operation === '/' && num2 === 0) {
                                    result = 'Error';
                                } else {
                                    switch (operation) {
                                        case '+': result = num1 + num2; break;
                                        case '-': result = num1 - num2; break;
                                        case '×': result = num1 * num2; break;
                                        case '/': result = num1 / num2; break;
                                        default: return;
                                    }
                                    result = parseFloat(result.toFixed(10)); // Prevent long decimals
                                }
                                
                                neonCalcHistoryItems.unshift({ expression, result });
                                if(neonCalcHistoryItems.length > 5) neonCalcHistoryItems.pop();

                                currentInput = result.toString();
                                operation = null; previousInput = ''; resetInputNext = true;
                                updateNeonCalcHistory();
                            }
                        } else if (value === '±') {
                            currentInput = (parseFloat(currentInput) * -1).toString();
                        } else if (value === '%') {
                            // Percentage usually works with another number, e.g. X + Y% (of X)
                            // Simple implementation: currentInput / 100
                            if(previousInput && operation){ // e.g. 100 + 10 %  (10% of 100, then add)
                                currentInput = (parseFloat(previousInput) * (parseFloat(currentInput)/100)).toString();
                            } else { // 50 % (becomes 0.5)
                                currentInput = (parseFloat(currentInput) / 100).toString();
                            }
                            resetInputNext = true;

                        } else if (['+', '-', '×', '/'].includes(value)) {
                            if (operation && previousInput && !resetInputNext) { // Chain operations: 5 + 2 - (auto-calculate 5+2 first)
                                calcButtons.forEach(btn => { if(btn.textContent === '=') btn.click(); });
                            }
                            operation = value; previousInput = currentInput; resetInputNext = true;
                        } else { // Number or decimal point
                            if (value === '.' && currentInput.includes('.')) return; // Only one decimal
                            if (currentInput === '0' || resetInputNext) {
                                currentInput = value; resetInputNext = false;
                            } else {
                                currentInput += value;
                            }
                        }
                        updateNeonCalcDisplay();
                    });
                });
            }
            updateNeonCalcDisplay(); // Initial call


            // --- Age Calculator Functionality ---
            const birthDateInput = document.getElementById('birth-date');
            const compareDateInput = document.getElementById('compare-date');
            const calculateAgeBtn = document.getElementById('calculate-age');
            const ageResultDisplay = document.getElementById('age-result-display'); // Renamed
            const ageDetailsDiv = document.getElementById('age-details'); // Renamed
            const agePlaceholderDiv = document.getElementById('age-placeholder'); // Renamed
            const ageErrorDiv = document.getElementById('age-error'); // Renamed
            const ageHistoryListDiv = document.getElementById('age-history-list'); // Renamed
            const emptyAgeHistoryP = document.getElementById('empty-age-history');
            const clearAgeHistoryBtn = document.getElementById('clear-age-history');
            
            if(document.getElementById('today-button')) document.getElementById('today-button').addEventListener('click', () => { if(compareDateInput) compareDateInput.valueAsDate = new Date(); });
            if(document.getElementById('yesterday-button')) document.getElementById('yesterday-button').addEventListener('click', () => { const d = new Date(); d.setDate(d.getDate() - 1); if(compareDateInput) compareDateInput.valueAsDate = d; });
            if(document.getElementById('tomorrow-button')) document.getElementById('tomorrow-button').addEventListener('click', () => { const d = new Date(); d.setDate(d.getDate() + 1); if(compareDateInput) compareDateInput.valueAsDate = d; });
            if(document.getElementById('clear-compare-date-button')) document.getElementById('clear-compare-date-button').addEventListener('click', () => { if(compareDateInput) compareDateInput.value = ''; });
            
            if (calculateAgeBtn) {
                calculateAgeBtn.addEventListener('click', function() {
                    if (!birthDateInput || !compareDateInput) return;
                    const birthD = birthDateInput.value;
                    if (!birthD) { showErrorAge("Please enter your birth date"); return; }
                    
                    const birthDate = new Date(birthD);
                    const compareDate = compareDateInput.value ? new Date(compareDateInput.value) : new Date();
                    
                    if (birthDate > compareDate) { showErrorAge("Birth date cannot be after the compare date"); return; }
                    
                    const age = calculateExactAge(birthDate, compareDate);
                    displayAgeResults(age, birthDate, compareDate);
                    addAgeToHistory(age, birthDate, compareDate);
                });
            }
            
            if (clearAgeHistoryBtn) {
                clearAgeHistoryBtn.addEventListener('click', function() {
                    if(ageHistoryListDiv && emptyAgeHistoryP) {
                        ageHistoryListDiv.innerHTML = ''; // Clear visual list
                        ageHistoryListDiv.appendChild(emptyAgeHistoryP); // Add back placeholder
                        emptyAgeHistoryP.classList.remove('hidden');
                    }
                    localStorage.removeItem('ageHistory');
                });
            }
            
            function calculateExactAge(birthDate, compareDate) {
                let years = compareDate.getFullYear() - birthDate.getFullYear();
                let months = compareDate.getMonth() - birthDate.getMonth();
                let days = compareDate.getDate() - birthDate.getDate();
                
                if (days < 0) {
                    months--;
                    const prevMonth = new Date(compareDate.getFullYear(), compareDate.getMonth(), 0);
                    days += prevMonth.getDate();
                }
                if (months < 0) {
                    years--;
                    months += 12;
                }
                
                const totalDays = Math.floor((compareDate - birthDate) / (1000 * 60 * 60 * 24));
                
                let nextBirthday = new Date(compareDate.getFullYear(), birthDate.getMonth(), birthDate.getDate());
                if (nextBirthday < compareDate || (nextBirthday.getTime() === compareDate.getTime() && birthDate.getTime() !== compareDate.getTime())) { // if today is birthday, next is next year
                    nextBirthday.setFullYear(compareDate.getFullYear() + 1);
                }
                const daysRemaining = Math.ceil((nextBirthday - compareDate) / (1000 * 60 * 60 * 24));
                
                const zodiac = getZodiacSign(birthDate);
                return { years, months, days, totalDays, nextBirthday, daysRemaining, zodiac };
            }
            
            function displayAgeResults(age, birthDate, compareDate) {
                if(agePlaceholderDiv) agePlaceholderDiv.classList.add('hidden');
                if(ageErrorDiv) ageErrorDiv.classList.add('hidden');
                
                if(document.getElementById('years-value')) document.getElementById('years-value').textContent = age.years;
                if(document.getElementById('months-value')) document.getElementById('months-value').textContent = age.months;
                if(document.getElementById('days-value')) document.getElementById('days-value').textContent = age.days;
                if(ageResultDisplay) ageResultDisplay.classList.remove('hidden');
                
                const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
                if(document.getElementById('birth-date-display')) document.getElementById('birth-date-display').textContent = birthDate.toLocaleDateString('en-US', options);
                if(document.getElementById('compare-date-display')) document.getElementById('compare-date-display').textContent = compareDate.toLocaleDateString('en-US', options);
                if(document.getElementById('total-days')) document.getElementById('total-days').textContent = age.totalDays + (age.totalDays === 1 ? ' day' : ' days');
                if(document.getElementById('next-birthday')) document.getElementById('next-birthday').textContent = age.nextBirthday.toLocaleDateString('en-US', options);
                if(document.getElementById('days-remaining')) document.getElementById('days-remaining').textContent = age.daysRemaining + (age.daysRemaining === 1 ? ' day' : ' days');
                
                if(document.getElementById('zodiac-sign')) document.getElementById('zodiac-sign').textContent = age.zodiac.sign;
                if(document.getElementById('zodiac-dates')) document.getElementById('zodiac-dates').textContent = age.zodiac.dates;
                if(document.getElementById('zodiac-icon')) document.getElementById('zodiac-icon').innerHTML = `<i class="fas ${age.zodiac.icon} text-yellow-500 text-xl"></i>`;
                
                if(ageDetailsDiv) ageDetailsDiv.classList.remove('hidden');
            }
            
            function showErrorAge(message) { // Renamed
                if(agePlaceholderDiv) agePlaceholderDiv.classList.add('hidden');
                if(ageResultDisplay) ageResultDisplay.classList.add('hidden');
                if(ageDetailsDiv) ageDetailsDiv.classList.add('hidden');
                
                if(document.getElementById('error-message')) document.getElementById('error-message').textContent = message;
                if(ageErrorDiv) ageErrorDiv.classList.remove('hidden');
            }
            
            function addAgeToHistory(age, birthDate, compareDate) {
                if (!ageHistoryListDiv || !emptyAgeHistoryP) return;
                const historyItem = document.createElement('div');
                historyItem.className = 'bg-gray-50 p-4 rounded-lg border border-gray-200 fade-in'; // Uses shared style
                const options = { year: 'numeric', month: 'short', day: 'numeric' };

                historyItem.innerHTML = `
                    <div class="flex justify-between items-center mb-2">
                        <div class="font-medium">Born: ${birthDate.toLocaleDateString('en-US', options)}</div>
                        <div class="text-sm text-gray-500">As of: ${compareDate.toLocaleDateString('en-US', options)}</div>
                    </div>
                    <div class="grid grid-cols-3 gap-2 text-center">
                        <div class="bg-purple-50 p-2 rounded"><div class="text-xs text-purple-600">Years</div><div class="font-bold">${age.years}</div></div>
                        <div class="bg-purple-50 p-2 rounded"><div class="text-xs text-purple-600">Months</div><div class="font-bold">${age.months}</div></div>
                        <div class="bg-purple-50 p-2 rounded"><div class="text-xs text-purple-600">Days</div><div class="font-bold">${age.days}</div></div>
                    </div>`;
                
                emptyAgeHistoryP.classList.add('hidden');
                ageHistoryListDiv.insertBefore(historyItem, ageHistoryListDiv.firstChild);
                
                saveCurrentAgeHistory();
            }
            
            function saveCurrentAgeHistory() {
                if (!ageHistoryListDiv) return;
                const items = [];
                // Storing simplified data for localStorage to avoid large HTML strings
                ageHistoryListDiv.querySelectorAll('div.fade-in').forEach(itemDiv => {
                     // This part needs to be rethought if we want to reconstruct from data, not HTML.
                     // For now, let's assume we store the data object that created the item.
                     // This requires changing how `addAgeToHistory` interacts with storage.
                     // Let's simplify: store the raw HTML for now as per original logic.
                    items.push(itemDiv.innerHTML); 
                });
                if(items.length > 0) localStorage.setItem('ageHistory', JSON.stringify(items.slice(0,5))); // Limit stored history
            }
            
            function loadAgeHistory() {
                if (!ageHistoryListDiv || !emptyAgeHistoryP) return;
                const savedHistory = localStorage.getItem('ageHistory');
                if (savedHistory) {
                    const itemsHTML = JSON.parse(savedHistory);
                    if (itemsHTML.length > 0) {
                        emptyAgeHistoryP.classList.add('hidden');
                        itemsHTML.forEach(itemHtml => {
                            const historyItem = document.createElement('div');
                            historyItem.className = 'bg-gray-50 p-4 rounded-lg border border-gray-200 fade-in'; // Re-apply class
                            historyItem.innerHTML = itemHtml;
                            ageHistoryListDiv.appendChild(historyItem); // Append, not prepend, to maintain order from storage
                        });
                    }
                }
            }
            
            function getZodiacSign(birthDate) {
                const day = birthDate.getDate();
                const month = birthDate.getMonth() + 1; // Jan = 1
                if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return { sign: "Aquarius", dates: "Jan 20 - Feb 18", icon: "fa-tint" }; // fa-water is not standard, fa-tint or fa-wind
                if ((month === 2 && day >= 19) || (month === 3 && day <= 20)) return { sign: "Pisces", dates: "Feb 19 - Mar 20", icon: "fa-fish" };
                if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return { sign: "Aries", dates: "Mar 21 - Apr 19", icon: "fa-pastafarianism" }; // fa-ram is not standard, using something else
                if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return { sign: "Taurus", dates: "Apr 20 - May 20", icon: "fa-hat-cowboy" }; // fa-bull is not standard
                if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return { sign: "Gemini", dates: "May 21 - Jun 20", icon: "fa-users" };
                if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return { sign: "Cancer", dates: "Jun 21 - Jul 22", icon: "fa-moon" }; // fa-crab is not standard
                if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return { sign: "Leo", dates: "Jul 23 - Aug 22", icon: "fa-crown" }; // fa-lion is not standard
                if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return { sign: "Virgo", dates: "Aug 23 - Sep 22", icon: "fa-seedling" }; // fa-virgo is not standard
                if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return { sign: "Libra", dates: "Sep 23 - Oct 22", icon: "fa-balance-scale" };
                if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return { sign: "Scorpio", dates: "Oct 23 - Nov 21", icon: "fa-spider" }; // fa-scorpion is not standard
                if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return { sign: "Sagittarius", dates: "Nov 22 - Dec 21", icon: "fa-bullseye" }; // fa-arrow-right is too generic
                if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return { sign: "Capricorn", dates: "Dec 22 - Jan 19", icon: "fa-mountain" }; // fa-goat is not standard
                return { sign: "Unknown", dates: "", icon: "fa-question-circle"};
            }
            loadAgeHistory(); // Initial call for age calculator
        });
    </script>
</body>
</html>
